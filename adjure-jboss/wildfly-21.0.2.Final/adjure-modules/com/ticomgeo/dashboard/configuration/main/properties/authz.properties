###
# This file is only used if the shiro .ini files are configured to do CASPORT/GOVPORT
# The two main things in here are:
# 1) properties that specify the CASPORT or GOVPORT host:
#     attributeprovider.host=sim-service-demo.ticom-geo.com
#     etc.
# 2) properties that specify additional access control policy enforcment rules:
#     ssose.policy.attributepolicynames=country
#     etc.
###

###
# Access control policy enforcement rules
# The following access control policy properties are only used if the ini files are configured to do CASPORT/GOVPORT
# AND to use the SsoPep (PolicyEnforcementPoint) realm, i.e. if this line is uncommented in the shiro ini:
#    authZRealm = com.ticomgeo.shiro.realm.X509CertSsoPepRealm
###
# List of policy names:
#ssose.policy.attributepolicynames=country

# The attribute in a policy rule (i.e country) MUST
# be a value also in the attributeprovider.deliverAttributes property list below
#country.policy=country is USA

# Examples:
#ssose.policy.attributepolicynames=country,dissemcontrol,clearances
#dissemcontrol.policy=dissemControl is NOFORN
#clearances.policy=clearances is "TOP SECRET"
#Values:
#clearances values: CONFIDENTIAL, SECRET, TOP SECRET, UNCLASSIFIED
#visas values: NOFORN, FVEY, ACGU, USA, GBR, NZL, AUS
#dissemControl: NF 
#dutyorg: NSA,USN,

# Note that the truststore used by the secure transport in the GovPort attribute client
# makes use of the truststore configuration in the pki.properties file
attributeprovider.scheme=https

attributeprovider.host=sim-service-demo.ticom-geo.com
attributeprovider.port=8443
attributeprovider.resource=govport/rs/users/{id}/info
attributeprovider.role.resource=govport/rs/users/{id}/roles
attributeprovider.policy.resource=govport/rs/users/{id}/policies/{projectname}
attributeprovider.policy.auth.resource=govport/rs/users/{id}/policies/{policyname}/authorization
attributeprovider.access.auth.resource=govport/rs/users/{id}/accesses/{markings}

# This is the highside GOVPORT service on j-net
#attributeprovider.scheme=https
# The GOVPORT production environment:
#attributeprovider.host=soa.iaa.cloud.coe.ic.gov
# The  GOVPORT development environment, should only be used for initial GOVPORT integration testing
#attributeprovider.host=soa.ede.iaa.cloud.coe.ic.gov

# This is the highside CASPORT service on n-net:
#attributeprovider.scheme=https
# The CASPORT production environment:
#attributeprovider.host=soa.casport.ssdc.proj.nsa.ic.gov
# The CASPORT development environment, should only be used for initial CASPORT integration testing
#attributeprovider.host=soa.ede.casport.ssdc.proj.nsa.ic.gov

#attributeprovider.port=443
#attributeprovider.resource=rest/v3/users/{id}/info


# These next URL's are not used in production, are only present for future testing if necessary.
# These are only used if you set the doRoleAttrQuery, doPolicyAttrQuery, or doPolicyAuthAttrQuery properties below to true:
# Do not enable that unless you know what you are doing...
#attributeprovider.role.resource=rest/v3/users/{id}/roles
#attributeprovider.policy.resource=rest/v3/users/{id}/policies/{projectname}
#attributeprovider.policy.auth.resource=rest/v3/users/{id}/policies/{policyname}/authorization
#attributeprovider.access.auth.resource=rest/v3/users/{id}/accesses/{markings}

#attributeprovider.policy.projectname=
#attributeprovider.policyname=

# If the AttributeProvider service is down, should we allow or deny by default:
attributeprovider.providerDown=deny
#attributeprovider.providerDown=deny

# If the AttributeProvider service is up but doesn't know about a given user DN, should we allow or deny by default:
attributeprovider.unknownResponse=deny
#attributeprovider.unknownResponse=allow

# If there is no AttributeProvider service and we shouldn't even attempt to query anything, set this to true:
attributeprovider.skipAttributeQuery=false

# Among the many attributes returned by CASPORT/GOVPORT, which ones should get delivered downstream:
# This list must include the attribute that is in any policy rule above:
attributeprovider.deliverAttributes=fullName,uid,email,country,clearances,dissemControl
# keys associated with the attribute query:
# dn,fullName,firstName,lastName,email,uid,country,isIcMember,dutyorg
# list valued attributes:
# clearances,cois,visas,formalGroups,organizations,affiliations,dissemControl
#
# In the shiro file containing the PKI cert realm, if you enable any of these:
# doRoleAttrQuery=true
# doPolicyAttrQuery=true
# doPolicyAuthAttrQuery=true
# then you must add these keys to the deliverAttributes list above:
# keys associated with the role query:
# roles
#
# keys associated with the policy query:
# policy
#
# keys associated with the policy authorization query:
# policy,info,authorized
#
# keys associated with the accesses authorization query:
# marking,info,authorized

