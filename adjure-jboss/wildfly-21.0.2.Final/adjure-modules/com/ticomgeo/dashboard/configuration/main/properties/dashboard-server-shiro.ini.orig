[main]

####################################################################################################
# This is a note on the format of the data in this file
##################################################
#
# This file is broken down into (sometimes nested) sections. Each section is identified by a
# commented line where the first word on the line starts with '@@'. All the characters between the
# first '@@' characters and the next '@@' characters identify the name of the section. All the lines
# between the first occurrence of that section name and the second occurrence of the same name are
# part of that section.
#
# When a section is enabled, all "commented property lines" in that section are uncommented. A
# commented property line is a line that starts with '#' and is followed by 'property=value' (where
# 'property' is the name of the property, and 'value' is the value for that property. Normal comment
# lines (like this one) should not follow that format (don't create a line that looks like '# a=b'
# unless that line is intended to define a property 'a' with a value of 'b' when that section is
# enabled). If a section has a nested section, the nested section is enabled/disabled as appropriate
# only if the outer section is enabled as well. This means that enabling the outer section does NOT
# enable all the sections inside it. Instead, it just enables the properties that are in the outer
# section, not those in any nested sections.
#
# The set-shiro-mode-single-file.pl perl script (run by the set-shiro-mode.sh script) take care of
# commenting or uncommenting the lines in a given section based on which behavior is desired.
#
# There are two overall modes of behavior: PKI and AUTO
#
# With PKI ssl is enabled, and with AUTO it is disabled
#
# PKI has further sub-sections dealing with CASPORT and production vs test environments 
#
####################################################################################################

##################################################
# Define SSL filter
#
# Override HTTP and HTTPS port using system property (if set)
# Set default ports for both HTTP and HTTPS
#
sslFilter = com.ticomgeo.shiro.filter.SslFilter
sslFilter.httpsPortProperty = jboss.https.port
sslFilter.defaultHttpsPort = 8443
sslFilter.httpPortProperty = jboss.http.port
sslFilter.defaultHttpPort = 8080

##################################################
# Start 'PKI' section
#
# @@PKI@@
# This section enables PKI based authentication. When PKI authentication is not
# desired, and you want "auto" login of a default userId with no authentication,
# and no SSL, then all lines in this section should be commented out.
#
##################################################

#
# When enabling this authentication model is desired, we also need to enable ssl
#
sslFilter.enabled=true

authCFilter = com.ticomgeo.shiro.filter.X509CertAuthenticatingFilter
authCFilter.principalSource = dn
authCFilter.wantLoginDelegate = false

##################################################
# Start 'production certificate failure' section (nested in PKI)
#
# @@CERT FAILURE PROD@@
# This section contains the failure message to display in production when a
# certificate failure occurs
##################################################

authCFilter.certFailureMessage = Contact <a href='mailto:<EMAIL>?Subject=PKI%20Certificate' target='_top'>here</a> for assistance with PKI certificate questions.

##################################################
# @@CERT FAILURE PROD@@
#
# End 'production certificate failure' section (nested in PKI)
##################################################

##################################################
# Start 'test certificate failure' section (nested in PKI)
#
# @@CERT FAILURE TEST@@
# This section contains the failure message to display in a test environment.
# This message should be used only in an unclassified TGI testing environment
##################################################

#authCFilter.certFailureMessage = To acquire a PKI certificate for unclassified testing, generate one <a href="http://sim-service-demo.ticom-geo.com:8989/users.html">here</a> and import it into your browser.

##################################################
# @@CERT FAILURE TEST@@
#
# End 'test certificate failure' section (nested in PKI)
##################################################

##################################################
# Start default PKI authZRealm section (nested in PKI)
#
# @@REALM PKI@@
# This authZRealm is the default for PKI authentication. This authorization realm
# does PKI user authentication only. It does not do CASPORT/GOVPORT
##################################################

authZRealm = com.ticomgeo.shiro.realm.X509CertRealm

##################################################
# @@REALM PKI@@
#
# End default PKI authZRealm section (nested in PKI)
##################################################

##################################################
# Start CASPORT PKI authZRealm section (nested in PKI)
#
# @@REALM CASPORT@@
# If integration with CASPORT/GOVPORT is desired, comment the previous
# authZRealm and uncomment only one of the next two authZRealm lines.
# The same choice must also be enabled in the dashboardwebclient-shiro.ini
# (for the Dashboard UI application).
##################################################

##################################################
# Start CASPORT PKI authZRealm attributes-only section (nested in PKI/CASPORT)
#
# @@REALM CASPORT ATTR@@
# This realm implementation only queries CASPORT/GOVPORT for the given user's
# attributes. It does not enforce any additional access control rules.

#authZRealm = com.ticomgeo.shiro.realm.X509CertRoleAttrRealm

##################################################
# @@REALM CASPORT ATTR@@
#
# End CASPORT PKI authZRealm attributes-only section (nested in PKI/CASPORT)
##################################################

##################################################
# Start CASPORT PKI authZRealm policy roles section (nested in PKI/CASPORT)
#
# @@REALM CASPORT CR@@
# This realm enforces additional policy rules (i.e. country=USA) based on the
# user's attributes from CASPORT/GOVPORT. Those rules are defined in the
# authz.properties file.
##################################################

#authZRealm = com.ticomgeo.shiro.realm.X509CertSsoPepRealm

##################################################
# @@REALM CASPORT CR@@
#
# End CASPORT PKI authZRealm policy roles section (nested in PKI/CASPORT)
##################################################

####
# This line must be uncommented if either of the previous two CASPORT/GOVPORT
# based realms are used

#authZRealm.attributeProviderType=govportAttributeProvider

##################################################
# @@REALM CASPORT@@
#
# End CASPORT PKI authZRealm section (nested in PKI)
##################################################

authZRealm.doValidation = true
authZRealm.doDateValidation = true
#
# The OCSP validation host is specified in the pki.properties file
#
authZRealm.doRevokeValidation = true
authZRealm.failOnCertError = true
authZRealm.revokeCheckOrder = ocsp-only

##################################################
# @@PKI@@
#
# End 'PKI' section
##################################################

##################################################
# Start 'auto' section (no PKI, default user)
#
# @@AUTO@@
# This section does NOT do PKI authentication and does NOT do form based
# userId/password login. Instead, it does automatic authentication and login of
# a default userId specified in this file, OR does authc and login of a userId
# and role passed as query parameters. The query parameters are 'user' and
# 'role' (although the userId parameter is configurable below). To trigger a
# different userId and/or role, load the dashboard UI this way:
#
##   http://<host>:8080/dashboard?user=fred&role=ADMIN

#
# When enabling this authentication model, we also disable ssl
#
#sslFilter.enabled=false

#authCFilter = com.ticomgeo.shiro.filter.AuthcFilter
#authCFilter.uidParam = user
#authCFilter.defaultUid = admin
#authCFilter.permittedRoles = ADMIN,OPERATOR
#authZRealm = com.ticomgeo.shiro.realm.UserPassRealm
#authZRealm.defaultRoles = ADMIN,OPERATOR

##################################################
# @@AUTO@@
#
# End 'auto' section (no PKI, default user)
##################################################

####
# Common settings for all configurations
####
userNoRedirect = com.ticomgeo.shiro.filter.NoRedirectUserFilter

##################################################
# Infinispan cache naming conventions
#
# Every infinispan cache that is referenced must be defined the following places:
# 1) web.xml of at least one application that references that cache (preferrably all such applications)
# 2) standalone.xml in the "infinispan" subsystem (see below)
# 3) each realm in this file
#
#####################################
# web.xml requirements
#
# The web.xml must include, for each cache that is referenced/identified in this file, the following:
#
#    <resource-ref>
#      <res-ref-name>logical-cache-name</res-ref-name>
#      <lookup-name>java:jboss/infinispan/cache/container-name/cache-name</lookup-name>
#    </resource-ref>
#
# In the above:
#   logical-cache-name: A name that the application chooses. It does not need to match any name
#                       defined in this file. It can be used by application code to load the cache,
#                       if desired.
#   container-name:     The name of the cache container. This must match the 'containerName'
#                       assigned to the cacheManager below. If the cacheManager.containerName is not
#                       set, it defaults to 'shiro'.
#   cache-name:         The name of the cache within the cache container. This must match the
#                       name of a cache assigned to some AuthorizationRealm (using
#                       'authorizationCacheName'), or the name of a cache assigned to the
#                       sessionDAO assigned to the sessionManager (using
#                       sessionDAO.activeSessionsCacheName).
#
#####################################
#
#####################################
# Cache Manager
#####################################
# The cache is accessed through a cache manager (InfinispanCacheManager), and the cache manager is
# associated with a cache container (default='shiro'). The cache container must be defined in
# standalone.xml, in the 'infinispan' subsystem (see below), and is referenced through each web.xml
# relevant 'resource-ref' entry.
#
cacheManager = com.ticomgeo.shiro.cache.impl.InfinispanCacheManager
#
# This container name must be the same between this file and dashboardwebclient-shiro.ini
#
cacheManager.containerName = shiro

#####################################
# Realm authorization cache name
#####################################
# Every realm that extends a shiro 'AuthorizingRealm' is associated with an infinispan cache. The
# name of that cache defaults to the name of the realm as specified in this file, suffixed with
# '.authorizationCache'. In order to more predictably integrate with both the web.xml for the
# application and the wildfly standalone.xml, this name should be overridden. Generally, the name
# would convey the context where the authorization information should be shared. For example, if an
# application has a server war and a web client war, both of those would want to use the same
# authorization cache name, so the cache is shared between them, and the name would be something
# associated with that application. If more applications want to use that cache, then a more general
# name for the cache might be more appropriate. The name should NOT be the same as the
# authorizationCacheName for any other authorization realm, and should NOT be the same as the name
# for the sessionDAO cache.
#
#
# This cache name must be the same between this file and dashboardwebclient-shiro.ini
#
authZRealm.authorizationCacheName = dashboard-authorization

#####################################
# Session DAO cache name
#####################################
# Every sessionDAO that extends 'CachingSessionDAO' is associated with an infinispan cache. The name
# of that cache defaults to 'shiro-activeSessionCache'. In order to more predicatbly integrate with
# both the web.xml for the application and the wildfly standalone.xml, this name should be
# overridden. The name should be chosen similar to the way the realm authorization cache name is
# chosen. The name should NOT be the same as the authorizationCacheName for any authorization realm.
#
sessionDAO = org.apache.shiro.session.mgt.eis.EnterpriseCacheSessionDAO
sessionDAO.cacheManager = $cacheManager
#
# This cache name must be the same between this file and dashboardwebclient-shiro.ini
#
sessionDAO.activeSessionsCacheName = dashboard-active-sessions

#
# Associate the security manager with the cache manager
#
securityManager.cacheManager = $cacheManager
#
# Associate the security manager with the realm
#
securityManager.realm = $authZRealm

sessionManager = com.ticomgeo.shiro.session.WebSessionManager
sessionManager.sessionDAO = $sessionDAO

cookie = org.apache.shiro.web.servlet.SimpleCookie
cookie.name = DASHBOARD-JSESSIONID
cookie.httpOnly = true
cookie.maxAge = -1
#
# The cookie path is "/" since that is the path that is common to both the application webclient and the application server
#
cookie.path = /
sessionManager.sessionIdCookie = $cookie

# 3,600,000 milliseconds = 1 hour
# 1 day is 86400000 ms
# 1 week is 604800000 ms
sessionManager.globalSessionTimeout = 604800000

#
# Set the sessionManager for the securityManager to our sessionManager
#
securityManager.sessionManager = $sessionManager

# NOTE:
# The following section is not modified by the scripts that enable PKI or AUTO mode
#
# These urls are relative to the context-root the webapp is deployed under
# (/DashboardServer)
#
[urls]
#####################################
# Anonymous, non-SSL URLs
#
# Allow anon access for webservice callbacks and internal webservice endpoints.
# These must be first, above the /** entry below that triggers access control.
#
# We have no HTTP/web service callbacks or internal webservice entrypoints
# that need anon access. If we had any, they would look something like the
# following for each:
#
#/RfSensorTaskingCallback/** = noSessionCreation,anon
#####################################

#####################################
# Anonymous access URLs
#
# Allow anonymous (non-authenticated) access over SSL (if enabled) for the following URLs.
#####################################
/app/** = sslFilter,noSessionCreation,anon

#####################################
# Authentication required URLs
#
# Allow only authenticated access over SSL (if enabled) for the following URLs.
# If not authenticated, then redirect to error
#####################################
/** = sslFilter,authCFilter
#
# or allow no session creation, anonymous access
#
#/** = sslFilter,noSessionCreation,anon
