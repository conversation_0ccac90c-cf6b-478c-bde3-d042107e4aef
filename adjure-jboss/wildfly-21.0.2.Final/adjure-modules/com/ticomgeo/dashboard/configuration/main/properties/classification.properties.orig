# - Compartment and ReleaseTo support multiple values as a comma separated list.
# 
#  Valid values for Level, ReleaseTo and Compartment are controlled by XML
# restrictions specified in ClassificationTypes.xsd, repeated here:
#
# Level:       U,C,S,TS
# Compartment: SI,TK
# ReleaseTo:   FOUO,ROK,EY3,EY4,EY5,EY9,NOFOR
 
#System.Level=U
#System.Compartment=
#System.CompartmentExt=
#System.ReleaseTo=
#System.ReleaseToExt=

# The default system classification can be overwritten for Dashboard

#Dashboard.Level=U
#Dashboard.Compartment=
#Dashboard.CompartmentExt=
#Dashboard.ReleaseTo=
#Dashboard.ReleaseToExt=
