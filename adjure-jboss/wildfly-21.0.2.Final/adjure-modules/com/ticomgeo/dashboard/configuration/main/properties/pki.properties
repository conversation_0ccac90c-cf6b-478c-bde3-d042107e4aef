# Properties related to keystore and truststore.
#
# keystore.resource
# The name of the keystore on the classpath. (required)
# e.g. keystore.resource = dashboard-keystore.jks
#
# keystore.password
# The keystore password. (optional, default is changeit)
# e.g. keystore.password = secret
#
# keystore.keyalias
# The keystore key alias. Note that some consumers of this file expect only one key in the keystore. (optional)
# e.g. keystore.keyalias = dashboard-svr
#
# keystore.keypasswd
# The keystore key password (of the single key or the key identified by the key alias). (required)
# e.g. keystore.keypassword = secret
#
# truststore.resource
# The truststore resource on the classpath.
# e.g. truststore.resource = dashboard-truststore.jks
#
# truststore.password
# The truststore password. (optional, default is changeit)
# e.g. truststore.password = secret

keystore.resource = dashboard-keystore.jks
truststore.resource = dashboard-truststore.jks
keystore.password = secret
keystore.keypassword = secret
truststore.password = secret

# Properties related to user X.509 certificate validation:
# ocspresponder.uri
# This property specifies the URI of an OCSP responder service to query.
# When this property is set, it should override an AuthorityInfoAccess (the OCSP responder URI) extension
# that may be in a user's certificate. If this property is not set, the user's certificate may be checked for
# an OCSP responder URI (AuthorityInfoAccess) extension. If one exists, it will be extracted from the user's
# certificate and queried.

# ocspresponder.cert.resource
# This property specifies the name of the file containing the OCSP responder's public certificate.
# That cert must be in PEM format. When this property is set, the OCSP responder responses will be verified using
# this certificate. If this property is not set, then the default behavior of OCSP validation assumes that the
# OCSP responder responses are signed by the issuer of the user certificate being validated, and that issuer cert
# can be found in the trust store specified by the truststore.resource property

# crl.resource
# This property specifies the name of the CRL file to load, this file must be available on the class path. The CRL file
# must be in PEM format.

# .mil network:
#ocspresponder.uri = http://ocsp.disa.mil

# j-net:
#ocspresponder.uri = http://ocsp.dodiis.ic.gov

# n-net:
#ocspresponder.uri = http://rsp.ssdc.proj.nsa.ic.gov

# unclass TGI internal testing:
ocspresponder.uri = http://sim-service-demo.ticom-geo.com:29999

#ocspresponder.cert.resource = ocspResponderCert.pem
#crl.resource = crlFile.pem

 
