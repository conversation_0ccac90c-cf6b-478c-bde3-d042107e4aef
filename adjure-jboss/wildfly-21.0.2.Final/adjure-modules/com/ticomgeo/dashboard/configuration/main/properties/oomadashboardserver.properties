##
# Service session:
##
# The amount of time (in milliseconds) that a session should remain alive with no activity:
#service.session.expire.timeout=600000

##
# Service settings:
##
# The amount of time (in milliseconds) that a service should wait for a response to a request:
#service.request.timeout=60000


##
# OOMA Web Service settings:
##
# The amount of time (in milliseconds) to wait for an OOMA connection:
ooma.service.connect.timeout=30000
##
# The amount of time (in milliseconds) to wait for a response to an OOMA request:
ooma.service.request.timeout=30000
##
# How frequently (in minutes) to synchronize OOMA database(s) to local DB:
ooma.sync.frequency.minutes=15


