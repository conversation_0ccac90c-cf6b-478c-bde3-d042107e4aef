#################################################
# General configuration for the init.d scripts, #
# not necessarily for WildFly itself.           #
# default location: /etc/default/wildfly        #
#################################################

## Location of JDK
# JAVA_HOME="/usr/lib/jvm/default-java"

## Location of WildFly
# JBOSS_HOME="/opt/wildfly"

## The username who should own the process.
# JBOSS_USER=wildfly

## The mode WildFly should start, standalone or domain
# JBOSS_MODE=standalone

## Configuration for standalone mode
# JBOSS_CONFIG=standalone.xml

## Configuration for domain mode
# JBOSS_DOMAIN_CONFIG=domain.xml
# JBOSS_HOST_CONFIG=host-master.xml

## The amount of time to wait for startup
# STARTUP_WAIT=60

## The amount of time to wait for shutdown
# SHUTDOWN_WAIT=60

## Location to keep the console log
# JBOSS_CONSOLE_LOG="/var/log/wildfly/console.log"

## Additionals args to include in startup
# JBOSS_OPTS="--admin-only -b 127.0.0.1"
