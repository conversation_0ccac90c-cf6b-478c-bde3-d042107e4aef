<html>
<head>
<META http-equiv="Content-Type" content="text/html; charset=utf-8">
<meta http-equiv="Content-Type" content="text/html;charset=utf-8">
<link rel="stylesheet" type="text/css" href="licenses.css">
</head>
<body>
<h2>WildFly Full </h2>
<p>The following material has been provided for informational purposes only, and should not be relied upon or construed as a legal opinion or legal advice.</p>
<table>
<tr>
<th>Package Group</th><th>Package Artifact</th><th>Package Version</th><th>Remote Licenses</th><th>Local Licenses</th>
</tr>
<tr>
<td>antlr</td><td>antlr</td><td>2.7.7</td><td><a href="http://www.antlr2.org/download/antlr-2.7.7.tar.gz">The Antlr 2.7.7 License</a>
<br>
</td><td><a href="the antlr 2.7.7 license.txt">the antlr 2.7.7 license.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml</td><td>classmate</td><td>1.5.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.core</td><td>jackson-annotations</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.core</td><td>jackson-core</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.core</td><td>jackson-databind</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.dataformat</td><td>jackson-dataformat-yaml</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.datatype</td><td>jackson-datatype-jdk8</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.datatype</td><td>jackson-datatype-jsr310</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.jaxrs</td><td>jackson-jaxrs-base</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.jaxrs</td><td>jackson-jaxrs-json-provider</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.jackson.module</td><td>jackson-module-jaxb-annotations</td><td>2.10.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.fasterxml.woodstox</td><td>woodstox-core</td><td>6.0.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.github.ben-manes.caffeine</td><td>caffeine</td><td>2.8.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.github.fge</td><td>btf</td><td>1.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="https://spdx.org/licenses/LGPL-3.0+.html">GNU Lesser General Public License v3.0 or later</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="gnu lesser general public license v3.0 or later.txt">gnu lesser general public license v3.0 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.github.fge</td><td>jackson-coreutils</td><td>1.8</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="https://spdx.org/licenses/LGPL-3.0+.html">GNU Lesser General Public License v3.0 or later</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="gnu lesser general public license v3.0 or later.txt">gnu lesser general public license v3.0 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.github.fge</td><td>json-patch</td><td>1.9</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="https://spdx.org/licenses/LGPL-3.0+.html">GNU Lesser General Public License v3.0 or later</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="gnu lesser general public license v3.0 or later.txt">gnu lesser general public license v3.0 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.github.fge</td><td>msg-simple</td><td>1.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="https://spdx.org/licenses/LGPL-3.0+.html">GNU Lesser General Public License v3.0 or later</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="gnu lesser general public license v3.0 or later.txt">gnu lesser general public license v3.0 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.github.spullara.mustache.java</td><td>compiler</td><td>0.9.6</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.google.code.gson</td><td>gson</td><td>2.8.6</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.google.guava</td><td>guava</td><td>25.0-jre</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.googlecode.javaewah</td><td>JavaEWAH</td><td>1.1.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.h2database</td><td>h2</td><td>1.4.197</td><td><a href="https://fedoraproject.org/wiki/Licensing/MPLv2.0">Mozilla Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/epl-1.0.txt">Eclipse Public License 1.0</a>
<br>
</td><td><a href="mozilla public license 2.0.html">mozilla public license 2.0.html</a>
<br>
<a href="eclipse public license 1.0.txt">eclipse public license 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.io7m.xom</td><td>xom</td><td>1.2.10</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.microsoft.azure</td><td>azure-storage</td><td>8.6.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.squareup.okhttp3</td><td>okhttp</td><td>3.9.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.squareup.okio</td><td>okio</td><td>1.13.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.activation</td><td>jakarta.activation</td><td>1.2.1</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.faces</td><td>jsf-impl</td><td>2.3.14.SP01</td><td><a href="https://javaee.github.io/glassfish/LICENSE">Common Development and Distribution License 1.1</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="common development and distribution license 1.1.txt">common development and distribution license 1.1.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.istack</td><td>istack-commons-runtime</td><td>3.0.10</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.istack</td><td>istack-commons-tools</td><td>3.0.10</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.mail</td><td>jakarta.mail</td><td>1.6.5</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.xml.bind.external</td><td>relaxng-datatype</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.xml.bind.external</td><td>rngom</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.xml.fastinfoset</td><td>FastInfoset</td><td>1.2.13</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>com.sun.xml.messaging.saaj</td><td>saaj-impl</td><td>1.4.1.SP1</td><td><a href="https://javaee.github.io/glassfish/LICENSE">Common Development and Distribution License 1.1</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="common development and distribution license 1.1.txt">common development and distribution license 1.1.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>commons-beanutils</td><td>commons-beanutils</td><td>1.9.4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>commons-cli</td><td>commons-cli</td><td>1.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>commons-codec</td><td>commons-codec</td><td>1.14</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>commons-collections</td><td>commons-collections</td><td>3.2.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>commons-io</td><td>commons-io</td><td>2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>commons-lang</td><td>commons-lang</td><td>2.6</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>gnu.getopt</td><td>java-getopt</td><td>1.0.13</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.0-standalone.html">GNU Library General Public License v2 only</a>
<br>
</td><td><a href="gnu library general public license v2 only.txt">gnu library general public license v2 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.agroal</td><td>agroal-api</td><td>1.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.agroal</td><td>agroal-narayana</td><td>1.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.agroal</td><td>agroal-pool</td><td>1.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.jaegertracing</td><td>jaeger-core</td><td>0.34.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.jaegertracing</td><td>jaeger-thrift</td><td>0.34.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.netty</td><td>netty-all</td><td>4.1.51.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing.contrib</td><td>opentracing-concurrent</td><td>0.2.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing.contrib</td><td>opentracing-interceptors</td><td>0.0.4.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing.contrib</td><td>opentracing-jaxrs2</td><td>0.4.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing.contrib</td><td>opentracing-tracerresolver</td><td>0.1.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing.contrib</td><td>opentracing-web-servlet-filter</td><td>0.2.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing</td><td>opentracing-api</td><td>0.31.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing</td><td>opentracing-noop</td><td>0.31.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.opentracing</td><td>opentracing-util</td><td>0.31.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.reactivex.rxjava2</td><td>rxjava</td><td>2.2.19</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.reactivex.rxjava3</td><td>rxjava</td><td>3.0.4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0.txt">The Apache Software License, Version 2.0</a>
<br>
</td><td><a href="the apache software license, version 2.0.txt">the apache software license, version 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye.config</td><td>smallrye-config</td><td>1.6.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye.config</td><td>smallrye-config-common</td><td>1.6.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye.config</td><td>smallrye-config-source-file-system</td><td>1.6.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-fault-tolerance</td><td>4.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-fault-tolerance-api</td><td>4.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-fault-tolerance-core</td><td>4.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-health</td><td>2.2.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-jwt</td><td>2.0.13</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-metrics</td><td>2.4.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-open-api-core</td><td>2.0.9</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-open-api-jaxrs</td><td>2.0.9</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.smallrye</td><td>smallrye-opentracing</td><td>1.3.4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.undertow.jastow</td><td>jastow</td><td>2.0.9.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.undertow.js</td><td>undertow-js</td><td>1.0.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.undertow</td><td>undertow-core</td><td>2.2.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.undertow</td><td>undertow-servlet</td><td>2.2.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>io.undertow</td><td>undertow-websockets-jsr</td><td>2.2.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>jakarta.enterprise</td><td>jakarta.enterprise.cdi-api</td><td>2.0.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>jakarta.inject</td><td>jakarta.inject-api</td><td>1.0.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>jakarta.json.bind</td><td>jakarta.json.bind-api</td><td>1.0.2</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>jakarta.json</td><td>jakarta.json-api</td><td>1.1.6</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
<a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
<a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>jakarta.persistence</td><td>jakarta.persistence-api</td><td>2.2.3</td><td><a href="http://www.eclipse.org/legal/epl-2.0">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>jakarta.security.enterprise</td><td>jakarta.security.enterprise-api</td><td>1.0.2</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>jakarta.validation</td><td>jakarta.validation-api</td><td>2.0.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>javax.jws</td><td>jsr181-api</td><td>1.0-MR1</td><td><a href="https://javaee.github.io/glassfish/LICENSE">Common Development and Distribution License 1.1</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="common development and distribution license 1.1.txt">common development and distribution license 1.1.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>jaxen</td><td>jaxen</td><td>1.1.6</td><td><a href="http://www.opensource.org/licenses/BSD-3-Clause">BSD 3-Clause "New" or "Revised" License</a>
<br>
</td><td><a href="bsd 3-clause new or revised license.html">bsd 3-clause new or revised license.html</a>
<br>
</td>
</tr>
<tr>
<td>jboss.jaxbintros</td><td>jboss-jaxb-intros</td><td>1.0.3.GA</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>joda-time</td><td>joda-time</td><td>2.9.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>net.bytebuddy</td><td>byte-buddy</td><td>1.9.11</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>net.jcip</td><td>jcip-annotations</td><td>1.0</td><td><a href="http://creativecommons.org/licenses/by/2.5/legalcode">Creative Commons Attribution 2.5</a>
<br>
</td><td><a href="creative commons attribution 2.5.html">creative commons attribution 2.5.html</a>
<br>
</td>
</tr>
<tr>
<td>net.shibboleth.utilities</td><td>java-support</td><td>7.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/bsd.txt">The BSD License</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="the bsd license.txt">the bsd license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.aesh</td><td>aesh</td><td>2.4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.aesh</td><td>aesh-extensions</td><td>1.8</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.aesh</td><td>readline</td><td>2.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>activemq-artemis-native</td><td>1.0.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-amqp-protocol</td><td>2.10.1</td><td><a href="https://www.apache.org/licenses/LICENSE-2.0.txt">Apache License, Version 2.0</a>
<br>
</td><td><a href="apache license, version 2.0.txt">apache license, version 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-cli</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-commons</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-core-client</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-dto</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-hornetq-protocol</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-hqclient-protocol</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-jdbc-store</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-jms-client</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-jms-server</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-journal</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-ra</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-selector</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-server</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-service-extensions</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-stomp-protocol</td><td>2.10.1</td><td><a href="https://www.apache.org/licenses/LICENSE-2.0.txt">Apache License, Version 2.0</a>
<br>
</td><td><a href="apache license, version 2.0.txt">apache license, version 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.activemq</td><td>artemis-tools</td><td>2.10.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.avro</td><td>avro</td><td>1.7.6</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="http://www.gnu.org/prep/maintain/html_node/License-Notices-for-Other-Files.html">FSF All Permissive License</a>
<br>
<a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="fsf all permissive license.html">fsf all permissive license.html</a>
<br>
<a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.commons</td><td>commons-lang3</td><td>3.10</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-core</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-bindings-coloc</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-bindings-soap</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-bindings-xml</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-databinding-aegis</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-databinding-jaxb</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-features-clustering</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-features-logging</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-frontend-jaxws</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-frontend-simple</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-management</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-security</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-security-saml</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-transports-http</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-transports-http-hc</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-transports-jms</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-transports-local</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-ws-addr</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-wsdl</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-ws-mex</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-ws-policy</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-ws-rm</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-rt-ws-security</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-tools-common</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-tools-java2ws</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-tools-validator</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-tools-wsdlto-core</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-tools-wsdlto-databinding-jaxb</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf</td><td>cxf-tools-wsdlto-frontend-jaxws</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf.services.sts</td><td>cxf-services-sts-core</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf.services.ws-discovery</td><td>cxf-services-ws-discovery-api</td><td>3.3.7</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf.xjcplugins</td><td>cxf-xjc-boolean</td><td>3.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf.xjcplugins</td><td>cxf-xjc-bug986</td><td>3.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf.xjcplugins</td><td>cxf-xjc-dv</td><td>3.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf.xjcplugins</td><td>cxf-xjc-ts</td><td>3.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.cxf.xjc-utils</td><td>cxf-xjc-runtime</td><td>3.3.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.httpcomponents</td><td>httpasyncclient</td><td>4.1.4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.httpcomponents</td><td>httpclient</td><td>4.5.13</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.httpcomponents</td><td>httpcore</td><td>4.4.13</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.httpcomponents</td><td>httpcore-nio</td><td>4.4.13</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.httpcomponents</td><td>httpmime</td><td>4.5.13</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.james</td><td>apache-mime4j</td><td>0.6</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.lucene</td><td>lucene-analyzers-common</td><td>5.5.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.lucene</td><td>lucene-backward-codecs</td><td>5.5.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.lucene</td><td>lucene-core</td><td>5.5.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.lucene</td><td>lucene-facet</td><td>5.5.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.lucene</td><td>lucene-misc</td><td>5.5.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.lucene</td><td>lucene-queries</td><td>5.5.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.lucene</td><td>lucene-queryparser</td><td>5.5.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.neethi</td><td>neethi</td><td>3.1.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.qpid</td><td>proton-j</td><td>0.33.2</td><td><a href="https://www.apache.org/licenses/LICENSE-2.0.txt">Apache License, Version 2.0</a>
<br>
</td><td><a href="apache license, version 2.0.txt">apache license, version 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.santuario</td><td>xmlsec</td><td>2.1.4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.sshd</td><td>sshd-common</td><td>2.4.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.sshd</td><td>sshd-core</td><td>2.4.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.taglibs</td><td>taglibs-standard-compat</td><td>1.2.6-RC1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.taglibs</td><td>taglibs-standard-impl</td><td>1.2.6-RC1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.taglibs</td><td>taglibs-standard-spec</td><td>1.2.6-RC1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.thrift</td><td>libthrift</td><td>0.13.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.velocity</td><td>velocity-engine-core</td><td>2.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.ws.xmlschema</td><td>xmlschema-core</td><td>2.2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.wss4j</td><td>wss4j-bindings</td><td>2.2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.wss4j</td><td>wss4j-policy</td><td>2.2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.wss4j</td><td>wss4j-ws-security-common</td><td>2.2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.wss4j</td><td>wss4j-ws-security-dom</td><td>2.2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.wss4j</td><td>wss4j-ws-security-policy-stax</td><td>2.2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.apache.wss4j</td><td>wss4j-ws-security-stax</td><td>2.2.5</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.bitbucket.b_c</td><td>jose4j</td><td>0.7.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.bouncycastle</td><td>bcmail-jdk15on</td><td>1.65</td><td><a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.bouncycastle</td><td>bcpg-jdk15on</td><td>1.65</td><td><a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.bouncycastle</td><td>bcpkix-jdk15on</td><td>1.65</td><td><a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.bouncycastle</td><td>bcprov-jdk15on</td><td>1.65</td><td><a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.codehaus.jackson</td><td>jackson-core-asl</td><td>1.9.13.redhat-00007</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.codehaus.jackson</td><td>jackson-jaxrs</td><td>1.9.13.redhat-00007</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.codehaus.jackson</td><td>jackson-mapper-asl</td><td>1.9.13.redhat-00007</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.codehaus.jackson</td><td>jackson-xc</td><td>1.9.13.redhat-00007</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.codehaus.jettison</td><td>jettison</td><td>1.4.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.codehaus.woodstox</td><td>stax2-api</td><td>4.2.1</td><td><a href="http://repository.jboss.org/licenses/bsd.txt">The BSD License</a>
<br>
</td><td><a href="the bsd license.txt">the bsd license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.cryptacular</td><td>cryptacular</td><td>1.2.4</td><td><a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html">GNU Lesser General Public License v3.0 only</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v3.0 only.txt">gnu lesser general public license v3.0 only.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.dom4j</td><td>dom4j</td><td>2.1.3</td><td><a href="https://fedoraproject.org/wiki/Licensing/Plexus_Classworlds_License">Plexus Classworlds License</a>
<br>
</td><td><a href="plexus classworlds license.html">plexus classworlds license.html</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.jdt.core.compiler</td><td>ecj</td><td>4.6.1</td><td><a href="http://repository.jboss.org/licenses/epl-1.0.txt">Eclipse Public License 1.0</a>
<br>
</td><td><a href="eclipse public license 1.0.txt">eclipse public license 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.jgit</td><td>org.eclipse.jgit</td><td>5.9.0.202009080501-r</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.jgit</td><td>org.eclipse.jgit.ssh.apache</td><td>5.9.0.202009080501-r</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.config</td><td>microprofile-config-api</td><td>1.4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.fault-tolerance</td><td>microprofile-fault-tolerance-api</td><td>2.1.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.health</td><td>microprofile-health-api</td><td>2.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.jwt</td><td>microprofile-jwt-auth-api</td><td>1.1.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.metrics</td><td>microprofile-metrics-api</td><td>2.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.openapi</td><td>microprofile-openapi-api</td><td>1.1.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.opentracing</td><td>microprofile-opentracing-api</td><td>1.3.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse.microprofile.rest.client</td><td>microprofile-rest-client-api</td><td>1.4.0</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.eclipse</td><td>yasson</td><td>1.0.5</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
<a href="http://repository.jboss.org/licenses/epl-1.0.txt">Eclipse Public License 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
<a href="eclipse public license 1.0.txt">eclipse public license 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.fusesource.jansi</td><td>jansi</td><td>1.18</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish</td><td>jakarta.el</td><td>3.0.3.jbossorg-2</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish</td><td>jakarta.enterprise.concurrent</td><td>1.1.1</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish</td><td>jakarta.json</td><td>1.1.6</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
<a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
<a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish.jaxb</td><td>codemodel</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish.jaxb</td><td>jaxb-jxc</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish.jaxb</td><td>jaxb-runtime</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish.jaxb</td><td>jaxb-xjc</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish.jaxb</td><td>txw2</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish.jaxb</td><td>xsom</td><td>2.3.3-b02</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.glassfish.soteria</td><td>jakarta.security.enterprise</td><td>1.0.1-jbossorg-1</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate.common</td><td>hibernate-commons-annotations</td><td>5.0.5.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate</td><td>hibernate-core</td><td>5.3.20.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate</td><td>hibernate-envers</td><td>5.3.20.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate</td><td>hibernate-search-backend-jms</td><td>5.10.7.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate</td><td>hibernate-search-engine</td><td>5.10.7.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate</td><td>hibernate-search-orm</td><td>5.10.7.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate</td><td>hibernate-search-serialization-avro</td><td>5.10.7.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate.validator</td><td>hibernate-validator</td><td>6.0.21.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hibernate.validator</td><td>hibernate-validator-cdi</td><td>6.0.21.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hornetq</td><td>hornetq-commons</td><td>2.4.7.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hornetq</td><td>hornetq-core-client</td><td>2.4.7.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.hornetq</td><td>hornetq-jms-client</td><td>2.4.7.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-cachestore-jdbc</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-cachestore-remote</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-client-hotrod</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-commons</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-component-annotations</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-core</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-hibernate-cache-commons</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-hibernate-cache-spi</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan</td><td>infinispan-hibernate-cache-v53</td><td>11.0.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.infinispan.protostream</td><td>protostream</td><td>4.3.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jasypt</td><td>jasypt</td><td>1.9.3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.javassist</td><td>javassist</td><td>3.23.2-GA</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.mozilla.org/MPL/MPL-1.1.html">Mozilla Public License 1.1</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="mozilla public license 1.1.txt">mozilla public license 1.1.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jberet</td><td>jberet-core</td><td>1.3.7.Final</td><td><a href="http://repository.jboss.org/licenses/epl-1.0.txt">Eclipse Public License 1.0</a>
<br>
</td><td><a href="eclipse public license 1.0.txt">eclipse public license 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.activemq.artemis.integration</td><td>artemis-wildfly-integration</td><td>1.0.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.classfilewriter</td><td>jboss-classfilewriter</td><td>1.2.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.common</td><td>jboss-common-beans</td><td>2.0.1.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ejb3</td><td>jboss-ejb3-ext-api</td><td>2.3.0.Final</td><td><a href="http://www.gnu.org/licenses/lgpl-3.0-standalone.html">GNU Lesser General Public License v3.0 only</a>
<br>
</td><td><a href="gnu lesser general public license v3.0 only.txt">gnu lesser general public license v3.0 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.genericjms</td><td>generic-jms-ra-jar</td><td>2.0.8.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.hal</td><td>hal-console</td><td>3.2.11.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.invocation</td><td>jboss-invocation</td><td>1.6.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-common-api</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-common-impl</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-common-spi</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-core-api</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-core-impl</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-deployers-common</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-jdbc</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ironjacamar</td><td>ironjacamar-validator</td><td>1.4.23.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss</td><td>jandex</td><td>2.1.3.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss</td><td>jboss-dmr</td><td>1.5.1.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss</td><td>jboss-ejb-client</td><td>4.0.33.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss</td><td>jboss-iiop-client</td><td>1.0.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss</td><td>jboss-transaction-spi</td><td>7.6.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss</td><td>jboss-vfs</td><td>3.2.15.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.logging</td><td>commons-logging-jboss-logging</td><td>1.0.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.logging</td><td>jboss-logging</td><td>3.4.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.logging</td><td>jul-to-slf4j-stub</td><td>1.0.1.Final</td><td><a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.logmanager</td><td>jboss-logmanager</td><td>2.1.17.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.logmanager</td><td>log4j-jboss-logmanager</td><td>1.2.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.marshalling</td><td>jboss-marshalling</td><td>2.0.9.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.marshalling</td><td>jboss-marshalling-river</td><td>2.0.9.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.metadata</td><td>jboss-metadata-appclient</td><td>13.0.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.metadata</td><td>jboss-metadata-common</td><td>13.0.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.metadata</td><td>jboss-metadata-ear</td><td>13.0.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.metadata</td><td>jboss-metadata-ejb</td><td>13.0.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.metadata</td><td>jboss-metadata-web</td><td>13.0.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.mod_cluster</td><td>mod_cluster-container-spi</td><td>1.4.1.Final</td><td><a href="https://spdx.org/licenses/LGPL-3.0+.html">GNU Lesser General Public License v3.0 or later</a>
<br>
</td><td><a href="gnu lesser general public license v3.0 or later.txt">gnu lesser general public license v3.0 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.mod_cluster</td><td>mod_cluster-core</td><td>1.4.1.Final</td><td><a href="https://spdx.org/licenses/LGPL-3.0+.html">GNU Lesser General Public License v3.0 or later</a>
<br>
</td><td><a href="gnu lesser general public license v3.0 or later.txt">gnu lesser general public license v3.0 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.mod_cluster</td><td>mod_cluster-load-spi</td><td>1.4.1.Final</td><td><a href="https://spdx.org/licenses/LGPL-3.0+.html">GNU Lesser General Public License v3.0 or later</a>
<br>
</td><td><a href="gnu lesser general public license v3.0 or later.txt">gnu lesser general public license v3.0 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.modules</td><td>jboss-modules</td><td>1.10.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="https://enterprise.dejacode.com/licenses/public/indiana-extreme/?_list_filters=q%3Dindiana%2Bextreme#license-text">Indiana University Extreme! Lab Software License 1.1.1</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="indiana university extreme lab software license 1.1.1.html">indiana university extreme lab software license 1.1.1.html</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.msc</td><td>jboss-msc</td><td>1.4.12.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.compensations</td><td>compensations</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana</td><td>jbosstxbridge</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.jts</td><td>narayana-jts-idlj</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.jts</td><td>narayana-jts-integration</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.rts</td><td>restat-api</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.rts</td><td>restat-bridge</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.rts</td><td>restat-integration</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.rts</td><td>restat-util</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.txframework</td><td>txframework</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.narayana.xts</td><td>jbossxts</td><td>5.10.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.openjdk-orb</td><td>openjdk-orb</td><td>8.1.4.Final</td><td><a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.remoting</td><td>jboss-remoting</td><td>5.0.19.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.remotingjmx</td><td>remoting-jmx</td><td>3.0.4.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>jose-jwt</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-atom-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-cdi</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-client</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-client-microprofile</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-crypto</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-jackson2-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-jackson-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-jaxb-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-jaxrs</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-jettison-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-jsapi</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-json-binding-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-json-p-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-multipart-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-rxjava2</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-spring</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-validator-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.resteasy</td><td>resteasy-yaml-provider</td><td>3.13.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.seam.integration</td><td>jboss-seam-int-jbossas</td><td>7.0.0.GA</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.security</td><td>jboss-negotiation-common</td><td>3.0.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.security</td><td>jboss-negotiation-extras</td><td>3.0.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.security</td><td>jboss-negotiation-ntlm</td><td>3.0.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.security</td><td>jboss-negotiation-spnego</td><td>3.0.6.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.security</td><td>jbossxacml</td><td>2.0.8.Final</td><td><a href="https://spdx.org/licenses/BSD-3-Clause-No-Nuclear-License.html">BSD 3-Clause No Nuclear License</a>
<br>
</td><td><a href="bsd 3-clause no nuclear license.html">bsd 3-clause no nuclear license.html</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.slf4j</td><td>slf4j-jboss-logmanager</td><td>1.0.4.GA</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.annotation</td><td>jboss-annotations-api_1.3_spec</td><td>2.0.1.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.batch</td><td>jboss-batch-api_1.0_spec</td><td>2.0.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.ejb</td><td>jboss-ejb-api_3.2_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-2.0">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.el</td><td>jboss-el-api_3.0_spec</td><td>2.0.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
<a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
<a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
<a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
<a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.enterprise.concurrent</td><td>jboss-concurrency-api_1.0_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.faces</td><td>jboss-jsf-api_2.3_spec</td><td>3.0.0.SP04</td><td><a href="http://www.eclipse.org/legal/epl-2.0">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.interceptor</td><td>jboss-interceptors-api_1.2_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.jms</td><td>jboss-jms-api_2.0_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.management.j2ee</td><td>jboss-j2eemgmt-api_1.1_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-2.0">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.resource</td><td>jboss-connector-api_1.7_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.security.auth.message</td><td>jboss-jaspi-api_1.1_spec</td><td>2.0.1.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.security.jacc</td><td>jboss-jacc-api_1.5_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.servlet</td><td>jboss-servlet-api_4.0_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.servlet.jsp</td><td>jboss-jsp-api_2.3_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.transaction</td><td>jboss-transaction-api_1.3_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.websocket</td><td>jboss-websocket-api_1.1_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception">GNU General Public License v2.0 only, with Classpath exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license v2.0 only, with classpath exception.txt">gnu general public license v2.0 only, with classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.ws.rs</td><td>jboss-jaxrs-api_2.1_spec</td><td>2.0.1.Final</td><td><a href="http://www.eclipse.org/legal/epl-2.0">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.xml.bind</td><td>jboss-jaxb-api_2.3_spec</td><td>2.0.0.Final</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.xml.rpc</td><td>jboss-jaxrpc-api_1.1_spec</td><td>2.0.0.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.xml.soap</td><td>jboss-saaj-api_1.4_spec</td><td>1.0.2.Final</td><td><a href="http://www.eclipse.org/legal/epl-v20.html">Eclipse Public License 2.0</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="eclipse public license 2.0.txt">eclipse public license 2.0.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.spec.javax.xml.ws</td><td>jboss-jaxws-api_2.3_spec</td><td>2.0.0.Final</td><td><a href="http://repository.jboss.org/licenses/edl-1.0.txt">Eclipse Distribution License, Version 1.0</a>
<br>
</td><td><a href="eclipse distribution license, version 1.0.txt">eclipse distribution license, version 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss</td><td>staxmapper</td><td>1.3.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.stdio</td><td>jboss-stdio</td><td>1.1.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.threads</td><td>jboss-threads</td><td>2.4.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld.module</td><td>weld-ejb</td><td>3.1.5.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld.module</td><td>weld-jsf</td><td>3.1.5.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld.module</td><td>weld-jta</td><td>3.1.5.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld.module</td><td>weld-web</td><td>3.1.5.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld.probe</td><td>weld-probe-core</td><td>3.1.5.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld</td><td>weld-api</td><td>3.1.SP3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld</td><td>weld-core-impl</td><td>3.1.5.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.weld</td><td>weld-spi</td><td>3.1.SP3</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.cxf</td><td>jbossws-cxf-client</td><td>5.4.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.cxf</td><td>jbossws-cxf-factories</td><td>5.4.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.cxf</td><td>jbossws-cxf-jaspi</td><td>5.4.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.cxf</td><td>jbossws-cxf-resources</td><td>5.4.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.cxf</td><td>jbossws-cxf-server</td><td>5.4.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.cxf</td><td>jbossws-cxf-transports-udp</td><td>5.4.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.cxf</td><td>jbossws-cxf-transports-undertow</td><td>5.4.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws</td><td>jbossws-api</td><td>1.1.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws</td><td>jbossws-common</td><td>3.3.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws</td><td>jbossws-common-tools</td><td>1.3.2.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws</td><td>jbossws-spi</td><td>3.3.0.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.ws.projects</td><td>jaxws-undertow-httpspi</td><td>1.0.1.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.xnio.netty</td><td>netty-xnio-transport</td><td>0.1.9.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.xnio</td><td>xnio-api</td><td>3.8.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jboss.xnio</td><td>xnio-nio</td><td>3.8.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jctools</td><td>jctools-core</td><td>2.1.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jgroups.azure</td><td>jgroups-azure</td><td>1.3.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jgroups</td><td>jgroups</td><td>4.2.5.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jgroups.kubernetes</td><td>jgroups-kubernetes</td><td>1.0.15.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.jsoup</td><td>jsoup</td><td>1.8.3</td><td><a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
<a href="https://jsoup.org/license.html">The JSoup MIT License</a>
<br>
</td><td><a href="mit license.txt">mit license.txt</a>
<br>
<a href="the jsoup mit license.html">the jsoup mit license.html</a>
<br>
</td>
</tr>
<tr>
<td>org.jvnet.staxex</td><td>stax-ex</td><td>1.7.8</td><td><a href="https://javaee.github.io/glassfish/LICENSE">Common Development and Distribution License 1.1</a>
<br>
<a href="http://repository.jboss.org/licenses/gpl-2.0-ce.txt">GNU General Public License, Version 2 with the Classpath Exception</a>
<br>
</td><td><a href="common development and distribution license 1.1.txt">common development and distribution license 1.1.txt</a>
<br>
<a href="gnu general public license, version 2 with the classpath exception.txt">gnu general public license, version 2 with the classpath exception.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-core</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-profile-api</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-saml-api</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-saml-impl</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-security-api</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-security-impl</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-soap-api</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-xacml-api</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-xacml-impl</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-xacml-saml-api</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-xacml-saml-impl</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-xmlsec-api</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.opensaml</td><td>opensaml-xmlsec-impl</td><td>3.3.1</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.ow2.asm</td><td>asm</td><td>7.1</td><td><a href="http://www.opensource.org/licenses/BSD-3-Clause">BSD 3-Clause "New" or "Revised" License</a>
<br>
</td><td><a href="bsd 3-clause new or revised license.html">bsd 3-clause new or revised license.html</a>
<br>
</td>
</tr>
<tr>
<td>org.ow2.asm</td><td>asm-util</td><td>7.1</td><td><a href="http://www.opensource.org/licenses/BSD-3-Clause">BSD 3-Clause "New" or "Revised" License</a>
<br>
</td><td><a href="bsd 3-clause new or revised license.html">bsd 3-clause new or revised license.html</a>
<br>
</td>
</tr>
<tr>
<td>org.picketbox</td><td>picketbox</td><td>5.0.3.Final-redhat-00006</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketbox</td><td>picketbox-commons</td><td>1.0.0.final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketbox</td><td>picketbox-infinispan</td><td>5.0.3.Final-redhat-00006</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html">GNU Lesser General Public License v2.1 only</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 only.txt">gnu lesser general public license v2.1 only.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink.distribution</td><td>picketlink-wildfly8</td><td>2.5.5.SP12-redhat-00012</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-api</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-common</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-config</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-federation</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-idm-api</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-idm-impl</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-idm-simple-schema</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.picketlink</td><td>picketlink-impl</td><td>2.5.5.SP12-redhat-00009</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.projectodd.vdx</td><td>vdx-core</td><td>1.1.6</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.projectodd.vdx</td><td>vdx-wildfly</td><td>1.1.6</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.reactivestreams</td><td>reactive-streams</td><td>1.0.3</td><td><a href="http://creativecommons.org/publicdomain/zero/1.0/legalcode">Creative Commons Zero v1.0 Universal</a>
<br>
</td><td><a href="creative commons zero v1.0 universal.txt">creative commons zero v1.0 universal.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.slf4j</td><td>slf4j-api</td><td>1.7.30</td><td><a href="http://www.opensource.org/licenses/MIT">MIT License</a>
<br>
</td><td><a href="mit license.txt">mit license.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.client</td><td>wildfly-client-config</td><td>1.0.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.common</td><td>wildfly-common</td><td>1.5.4.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-cli</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-controller</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-controller-client</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-core-feature-pack-common</td><td>13.0.3.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://repository.jboss.org/licenses/apache-2.0.txt">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-core-feature-pack-ee-8-api</td><td>13.0.3.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://repository.jboss.org/licenses/apache-2.0.txt">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-core-management-client</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-core-management-subsystem</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-core-security</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-core-security-api</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-deployment-repository</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-deployment-scanner</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-discovery</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-domain-http-error-context</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-domain-http-interface</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-domain-management</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-elytron-integration</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-embedded</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-event-logger</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-host-controller</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-io</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-jar-runtime</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-jmx</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-launcher</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-logging</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-management-client-content</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-network</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-patching</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-platform-mbean</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-process-controller</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-protocol</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-remoting</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-request-controller</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-security-manager</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-server</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-threads</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.core</td><td>wildfly-version</td><td>13.0.3.Final</td><td><a href="http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html">GNU Lesser General Public License v2.1 or later</a>
<br>
<a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
<a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.discovery</td><td>wildfly-discovery-client</td><td>1.2.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>jipijapa-eclipselink</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>jipijapa-hibernate4-1</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>jipijapa-hibernate4-3</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>jipijapa-hibernate5</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>jipijapa-hibernate5-3</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>jipijapa-openjpa</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>jipijapa-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-java</td><td>2.1.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-linux-i386</td><td>2.1.0.SP01</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-linux-s390x</td><td>2.1.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-linux-x86_64</td><td>2.1.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-macosx-x86_64</td><td>2.1.0.SP01</td><td><a href="http://repository.jboss.org/licenses/apache-2.0.txt">Apache License Version 2.0</a>
<br>
</td><td><a href="apache license version 2.0.txt">apache license version 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-solaris-x86_64</td><td>2.1.0.SP01</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-windows-i386</td><td>2.1.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.openssl</td><td>wildfly-openssl-windows-x86_64</td><td>2.1.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security.elytron-web</td><td>undertow-server</td><td>1.8.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security.elytron-web</td><td>undertow-server-servlet</td><td>1.8.0.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-asn1</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-audit</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-auth</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-auth-server</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-auth-server-deprecated</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-auth-server-http</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-auth-server-sasl</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-auth-util</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-base</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-client</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-credential</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-credential-source-deprecated</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-credential-source-impl</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-credential-store</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-digest</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-basic</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-bearer</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-cert</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-deprecated</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-digest</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-external</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-form</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-spnego</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-sso</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-http-util</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-jacc</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-jaspi</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-json-util</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-jwt</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-keystore</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-mechanism</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-mechanism-digest</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-mechanism-gssapi</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-mechanism-http</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-mechanism-oauth2</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-mechanism-scram</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-password-impl</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-permission</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-provider-util</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-realm</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-realm-jdbc</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-realm-ldap</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-realm-token</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-anonymous</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-auth-util</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-deprecated</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-digest</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-entity</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-external</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-gs2</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-gssapi</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-localuser</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-oauth2</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-otp</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-plain</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-sasl-scram</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-security-manager</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-security-manager-action</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-ssl</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-tool</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-util</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-x500</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-x500-cert</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-x500-cert-acme</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-x500-cert-util</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-x500-deprecated</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.security</td><td>wildfly-elytron-x500-principal</td><td>1.13.1.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.transaction</td><td>wildfly-transaction-client</td><td>1.1.13.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-appclient</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-batch-jberet</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-bean-validation</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-client-all</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-api</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-common</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-ee-cache</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-ee-hotrod</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-ee-infinispan</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-ee-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-ejb-infinispan</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-ejb-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-infinispan-client</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-infinispan-extension</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-infinispan-marshalling</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-infinispan-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-jgroups-api</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-jgroups-extension</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-jgroups-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-marshalling-api</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-marshalling-jboss</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-marshalling-protostream</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-marshalling-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-server</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-service</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-singleton-api</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-singleton-extension</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-api</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-cache</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-container</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-extension</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-hotrod</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-infinispan</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-clustering-web-undertow</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-cmp</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-configadmin</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-connector</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-datasources-agroal</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-ee</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-ee-security</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-ejb3</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.wildfly-http-client</td><td>wildfly-http-client-common</td><td>1.1.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.wildfly-http-client</td><td>wildfly-http-ejb-client</td><td>1.1.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.wildfly-http-client</td><td>wildfly-http-naming-client</td><td>1.1.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly.wildfly-http-client</td><td>wildfly-http-transaction-client</td><td>1.1.2.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-iiop-openjdk</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jacorb</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jaxr</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jaxrs</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jdr</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jpa</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jsf</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jsf-injection</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-jsr77</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-mail</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-messaging</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-messaging-activemq</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-config-smallrye</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-fault-tolerance-smallrye-executor</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-fault-tolerance-smallrye-extension</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-health-smallrye</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-jwt-smallrye</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-metrics-smallrye</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-openapi-smallrye</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-opentracing-extension</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-microprofile-opentracing-smallrye</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-mod_cluster-extension</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-mod_cluster-undertow</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-naming</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-naming-client</td><td>1.0.13.Final</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-ormtransformer</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-picketlink</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-pojo</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-rts</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-sar</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-security</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-security-api</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-security-integration</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-security-plugins</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-security-vault-tool</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-system-jmx</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-transactions</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-undertow</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-web</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-web-common</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-webservices-server-integration</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld-bean-validation</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld-common</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld-ejb</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld-jpa</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld-spi</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld-transactions</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-weld-webservices</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.wildfly</td><td>wildfly-xts</td><td>21.0.2.Final</td><td><a href="http://repository.jboss.org/licenses/lgpl-2.1.txt">GNU Lesser General Public License v2.1 or later</a>
<br>
</td><td><a href="gnu lesser general public license v2.1 or later.txt">gnu lesser general public license v2.1 or later.txt</a>
<br>
</td>
</tr>
<tr>
<td>org.yaml</td><td>snakeyaml</td><td>1.26</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>wsdl4j</td><td>wsdl4j</td><td>1.6.3</td><td><a href="http://www.eclipse.org/legal/cpl-v10.html">Common Public License 1.0</a>
<br>
</td><td><a href="common public license 1.0.txt">common public license 1.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>xalan</td><td>serializer</td><td>2.7.1.jbossorg-4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>xalan</td><td>xalan</td><td>2.7.1.jbossorg-4</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>xerces</td><td>xercesImpl</td><td>2.12.0.SP03</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
<tr>
<td>xml-resolver</td><td>xml-resolver</td><td>1.2</td><td><a href="http://www.apache.org/licenses/LICENSE-2.0">Apache License 2.0</a>
<br>
</td><td><a href="apache license 2.0.txt">apache license 2.0.txt</a>
<br>
</td>
</tr>
</table>
</body>
</html>
