<?xml version="1.0" encoding="UTF-8"?>
<licenseSummary>
  <dependencies>
    <dependency>
      <groupId>antlr</groupId>
      <artifactId>antlr</artifactId>
      <version>2.7.7</version>
      <licenses>
        <license>
          <name>The Antlr 2.7.7 License</name>
          <url>http://www.antlr2.org/download/antlr-2.7.7.tar.gz</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml</groupId>
      <artifactId>classmate</artifactId>
      <version>1.5.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jdk8</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.datatype</groupId>
      <artifactId>jackson-datatype-jsr310</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.jaxrs</groupId>
      <artifactId>jackson-jaxrs-base</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.jaxrs</groupId>
      <artifactId>jackson-jaxrs-json-provider</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jaxb-annotations</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <version>2.8.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.github.fge</groupId>
      <artifactId>btf</artifactId>
      <version>1.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU Lesser General Public License v3.0 or later</name>
          <url>https://spdx.org/licenses/LGPL-3.0+.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.github.fge</groupId>
      <artifactId>jackson-coreutils</artifactId>
      <version>1.8</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU Lesser General Public License v3.0 or later</name>
          <url>https://spdx.org/licenses/LGPL-3.0+.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.github.fge</groupId>
      <artifactId>json-patch</artifactId>
      <version>1.9</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU Lesser General Public License v3.0 or later</name>
          <url>https://spdx.org/licenses/LGPL-3.0+.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.github.fge</groupId>
      <artifactId>msg-simple</artifactId>
      <version>1.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU Lesser General Public License v3.0 or later</name>
          <url>https://spdx.org/licenses/LGPL-3.0+.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.github.spullara.mustache.java</groupId>
      <artifactId>compiler</artifactId>
      <version>0.9.6</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.google.code.gson</groupId>
      <artifactId>gson</artifactId>
      <version>2.8.6</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>25.0-jre</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.h2database</groupId>
      <artifactId>h2</artifactId>
      <version>1.4.197</version>
      <licenses>
        <license>
          <name>Mozilla Public License 2.0</name>
          <url>https://fedoraproject.org/wiki/Licensing/MPLv2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Eclipse Public License 1.0</name>
          <url>http://repository.jboss.org/licenses/epl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.io7m.xom</groupId>
      <artifactId>xom</artifactId>
      <version>1.2.10</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.microsoft.azure</groupId>
      <artifactId>azure-storage</artifactId>
      <version>8.6.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.squareup.okhttp3</groupId>
      <artifactId>okhttp</artifactId>
      <version>3.9.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.squareup.okio</groupId>
      <artifactId>okio</artifactId>
      <version>1.13.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.faces</groupId>
      <artifactId>jsf-impl</artifactId>
      <version>2.3.14.SP01</version>
      <licenses>
        <license>
          <name>Common Development and Distribution License 1.1</name>
          <url>https://javaee.github.io/glassfish/LICENSE</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.istack</groupId>
      <artifactId>istack-commons-runtime</artifactId>
      <version>3.0.10</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.istack</groupId>
      <artifactId>istack-commons-tools</artifactId>
      <version>3.0.10</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.mail</groupId>
      <artifactId>jakarta.mail</artifactId>
      <version>1.6.5</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind.external</groupId>
      <artifactId>relaxng-datatype</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.bind.external</groupId>
      <artifactId>rngom</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.fastinfoset</groupId>
      <artifactId>FastInfoset</artifactId>
      <version>1.2.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.xml.messaging.saaj</groupId>
      <artifactId>saaj-impl</artifactId>
      <version>1.4.1.SP1</version>
      <licenses>
        <license>
          <name>Common Development and Distribution License 1.1</name>
          <url>https://javaee.github.io/glassfish/LICENSE</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>commons-beanutils</groupId>
      <artifactId>commons-beanutils</artifactId>
      <version>1.9.4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>commons-cli</groupId>
      <artifactId>commons-cli</artifactId>
      <version>1.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>commons-codec</groupId>
      <artifactId>commons-codec</artifactId>
      <version>1.14</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>commons-collections</groupId>
      <artifactId>commons-collections</artifactId>
      <version>3.2.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>commons-lang</groupId>
      <artifactId>commons-lang</artifactId>
      <version>2.6</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>gnu.getopt</groupId>
      <artifactId>java-getopt</artifactId>
      <version>1.0.13</version>
      <licenses>
        <license>
          <name>GNU Library General Public License v2 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.0-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.agroal</groupId>
      <artifactId>agroal-api</artifactId>
      <version>1.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.agroal</groupId>
      <artifactId>agroal-narayana</artifactId>
      <version>1.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.agroal</groupId>
      <artifactId>agroal-pool</artifactId>
      <version>1.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.jaegertracing</groupId>
      <artifactId>jaeger-core</artifactId>
      <version>0.34.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.jaegertracing</groupId>
      <artifactId>jaeger-thrift</artifactId>
      <version>0.34.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.netty</groupId>
      <artifactId>netty-all</artifactId>
      <version>4.1.51.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing</groupId>
      <artifactId>opentracing-api</artifactId>
      <version>0.31.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing</groupId>
      <artifactId>opentracing-noop</artifactId>
      <version>0.31.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing</groupId>
      <artifactId>opentracing-util</artifactId>
      <version>0.31.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing.contrib</groupId>
      <artifactId>opentracing-concurrent</artifactId>
      <version>0.2.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing.contrib</groupId>
      <artifactId>opentracing-interceptors</artifactId>
      <version>0.0.4.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing.contrib</groupId>
      <artifactId>opentracing-jaxrs2</artifactId>
      <version>0.4.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing.contrib</groupId>
      <artifactId>opentracing-tracerresolver</artifactId>
      <version>0.1.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.opentracing.contrib</groupId>
      <artifactId>opentracing-web-servlet-filter</artifactId>
      <version>0.2.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava2</groupId>
      <artifactId>rxjava</artifactId>
      <version>2.2.19</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.reactivex.rxjava3</groupId>
      <artifactId>rxjava</artifactId>
      <version>3.0.4</version>
      <licenses>
        <license>
          <name>The Apache Software License, Version 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-health</artifactId>
      <version>2.2.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-metrics</artifactId>
      <version>2.4.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-opentracing</artifactId>
      <version>1.3.4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye.config</groupId>
      <artifactId>smallrye-config</artifactId>
      <version>1.6.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye.config</groupId>
      <artifactId>smallrye-config-common</artifactId>
      <version>1.6.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye.config</groupId>
      <artifactId>smallrye-config-source-file-system</artifactId>
      <version>1.6.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.undertow.js</groupId>
      <artifactId>undertow-js</artifactId>
      <version>1.0.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jakarta.persistence</groupId>
      <artifactId>jakarta.persistence-api</artifactId>
      <version>2.2.3</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jakarta.security.enterprise</groupId>
      <artifactId>jakarta.security.enterprise-api</artifactId>
      <version>1.0.2</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>javax.jws</groupId>
      <artifactId>jsr181-api</artifactId>
      <version>1.0-MR1</version>
      <licenses>
        <license>
          <name>Common Development and Distribution License 1.1</name>
          <url>https://javaee.github.io/glassfish/LICENSE</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jaxen</groupId>
      <artifactId>jaxen</artifactId>
      <version>1.1.6</version>
      <licenses>
        <license>
          <name>BSD 3-Clause "New" or "Revised" License</name>
          <url>http://www.opensource.org/licenses/BSD-3-Clause</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jboss.jaxbintros</groupId>
      <artifactId>jboss-jaxb-intros</artifactId>
      <version>1.0.3.GA</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>joda-time</groupId>
      <artifactId>joda-time</artifactId>
      <version>2.9.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>net.bytebuddy</groupId>
      <artifactId>byte-buddy</artifactId>
      <version>1.9.11</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>net.jcip</groupId>
      <artifactId>jcip-annotations</artifactId>
      <version>1.0</version>
      <licenses>
        <license>
          <name>Creative Commons Attribution 2.5</name>
          <url>http://creativecommons.org/licenses/by/2.5/legalcode</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>net.shibboleth.utilities</groupId>
      <artifactId>java-support</artifactId>
      <version>7.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>The BSD License</name>
          <url>http://repository.jboss.org/licenses/bsd.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>activemq-artemis-native</artifactId>
      <version>1.0.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-amqp-protocol</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License, Version 2.0</name>
          <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-cli</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-commons</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-core-client</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-dto</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-hornetq-protocol</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-hqclient-protocol</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-jdbc-store</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-jms-client</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-jms-server</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-journal</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-ra</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-selector</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-server</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-service-extensions</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-stomp-protocol</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License, Version 2.0</name>
          <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.activemq</groupId>
      <artifactId>artemis-tools</artifactId>
      <version>2.10.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.avro</groupId>
      <artifactId>avro</artifactId>
      <version>1.7.6</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>FSF All Permissive License</name>
          <url>http://www.gnu.org/prep/maintain/html_node/License-Notices-for-Other-Files.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>3.10</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-core</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-bindings-coloc</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-bindings-soap</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-bindings-xml</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-databinding-aegis</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-databinding-jaxb</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-features-clustering</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-features-logging</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-frontend-jaxws</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-frontend-simple</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-management</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-security</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-security-saml</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-transports-http</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-transports-http-hc</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-transports-jms</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-transports-local</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-ws-addr</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-ws-mex</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-ws-policy</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-ws-rm</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-ws-security</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-rt-wsdl</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-tools-common</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-tools-java2ws</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-tools-validator</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-tools-wsdlto-core</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-tools-wsdlto-databinding-jaxb</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf</groupId>
      <artifactId>cxf-tools-wsdlto-frontend-jaxws</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf.services.sts</groupId>
      <artifactId>cxf-services-sts-core</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf.services.ws-discovery</groupId>
      <artifactId>cxf-services-ws-discovery-api</artifactId>
      <version>3.3.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf.xjc-utils</groupId>
      <artifactId>cxf-xjc-runtime</artifactId>
      <version>3.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf.xjcplugins</groupId>
      <artifactId>cxf-xjc-boolean</artifactId>
      <version>3.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf.xjcplugins</groupId>
      <artifactId>cxf-xjc-bug986</artifactId>
      <version>3.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf.xjcplugins</groupId>
      <artifactId>cxf-xjc-dv</artifactId>
      <version>3.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.cxf.xjcplugins</groupId>
      <artifactId>cxf-xjc-ts</artifactId>
      <version>3.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpasyncclient</artifactId>
      <version>4.1.4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore-nio</artifactId>
      <version>4.4.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpmime</artifactId>
      <version>4.5.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.james</groupId>
      <artifactId>apache-mime4j</artifactId>
      <version>0.6</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-analyzers-common</artifactId>
      <version>5.5.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-backward-codecs</artifactId>
      <version>5.5.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-core</artifactId>
      <version>5.5.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-facet</artifactId>
      <version>5.5.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-misc</artifactId>
      <version>5.5.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-queries</artifactId>
      <version>5.5.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.lucene</groupId>
      <artifactId>lucene-queryparser</artifactId>
      <version>5.5.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.neethi</groupId>
      <artifactId>neethi</artifactId>
      <version>3.1.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.qpid</groupId>
      <artifactId>proton-j</artifactId>
      <version>0.33.2</version>
      <licenses>
        <license>
          <name>Apache License, Version 2.0</name>
          <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.santuario</groupId>
      <artifactId>xmlsec</artifactId>
      <version>2.1.4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.taglibs</groupId>
      <artifactId>taglibs-standard-compat</artifactId>
      <version>1.2.6-RC1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.taglibs</groupId>
      <artifactId>taglibs-standard-impl</artifactId>
      <version>1.2.6-RC1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.taglibs</groupId>
      <artifactId>taglibs-standard-spec</artifactId>
      <version>1.2.6-RC1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.thrift</groupId>
      <artifactId>libthrift</artifactId>
      <version>0.13.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.velocity</groupId>
      <artifactId>velocity-engine-core</artifactId>
      <version>2.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.ws.xmlschema</groupId>
      <artifactId>xmlschema-core</artifactId>
      <version>2.2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.wss4j</groupId>
      <artifactId>wss4j-bindings</artifactId>
      <version>2.2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.wss4j</groupId>
      <artifactId>wss4j-policy</artifactId>
      <version>2.2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.wss4j</groupId>
      <artifactId>wss4j-ws-security-common</artifactId>
      <version>2.2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.wss4j</groupId>
      <artifactId>wss4j-ws-security-dom</artifactId>
      <version>2.2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.wss4j</groupId>
      <artifactId>wss4j-ws-security-policy-stax</artifactId>
      <version>2.2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.wss4j</groupId>
      <artifactId>wss4j-ws-security-stax</artifactId>
      <version>2.2.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcmail-jdk15on</artifactId>
      <version>1.65</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>1.65</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <version>1.65</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.codehaus.jackson</groupId>
      <artifactId>jackson-core-asl</artifactId>
      <version>1.9.13.redhat-00007</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.codehaus.jackson</groupId>
      <artifactId>jackson-jaxrs</artifactId>
      <version>1.9.13.redhat-00007</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.codehaus.jackson</groupId>
      <artifactId>jackson-mapper-asl</artifactId>
      <version>1.9.13.redhat-00007</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.codehaus.jackson</groupId>
      <artifactId>jackson-xc</artifactId>
      <version>1.9.13.redhat-00007</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.codehaus.jettison</groupId>
      <artifactId>jettison</artifactId>
      <version>1.4.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.cryptacular</groupId>
      <artifactId>cryptacular</artifactId>
      <version>1.2.4</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v3.0 only</name>
          <url>http://www.gnu.org/licenses/lgpl-3.0-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.dom4j</groupId>
      <artifactId>dom4j</artifactId>
      <version>2.1.3</version>
      <licenses>
        <license>
          <name>Plexus Classworlds License</name>
          <url>https://fedoraproject.org/wiki/Licensing/Plexus_Classworlds_License</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.config</groupId>
      <artifactId>microprofile-config-api</artifactId>
      <version>1.4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.health</groupId>
      <artifactId>microprofile-health-api</artifactId>
      <version>2.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.metrics</groupId>
      <artifactId>microprofile-metrics-api</artifactId>
      <version>2.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.opentracing</groupId>
      <artifactId>microprofile-opentracing-api</artifactId>
      <version>1.3.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.rest.client</groupId>
      <artifactId>microprofile-rest-client-api</artifactId>
      <version>1.4.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>codemodel</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>jaxb-jxc</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>jaxb-runtime</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>jaxb-xjc</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>txw2</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish.jaxb</groupId>
      <artifactId>xsom</artifactId>
      <version>2.3.3-b02</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish.soteria</groupId>
      <artifactId>jakarta.security.enterprise</artifactId>
      <version>1.0.1-jbossorg-1</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-core</artifactId>
      <version>5.3.20.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-envers</artifactId>
      <version>5.3.20.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-search-backend-jms</artifactId>
      <version>5.10.7.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-search-engine</artifactId>
      <version>5.10.7.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-search-orm</artifactId>
      <version>5.10.7.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate</groupId>
      <artifactId>hibernate-search-serialization-avro</artifactId>
      <version>5.10.7.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate.common</groupId>
      <artifactId>hibernate-commons-annotations</artifactId>
      <version>5.0.5.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator</artifactId>
      <version>6.0.21.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hibernate.validator</groupId>
      <artifactId>hibernate-validator-cdi</artifactId>
      <version>6.0.21.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hornetq</groupId>
      <artifactId>hornetq-commons</artifactId>
      <version>2.4.7.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hornetq</groupId>
      <artifactId>hornetq-core-client</artifactId>
      <version>2.4.7.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.hornetq</groupId>
      <artifactId>hornetq-jms-client</artifactId>
      <version>2.4.7.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-cachestore-jdbc</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-cachestore-remote</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-client-hotrod</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-commons</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-component-annotations</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-core</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-hibernate-cache-commons</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-hibernate-cache-spi</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan</groupId>
      <artifactId>infinispan-hibernate-cache-v53</artifactId>
      <version>11.0.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.infinispan.protostream</groupId>
      <artifactId>protostream</artifactId>
      <version>4.3.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jasypt</groupId>
      <artifactId>jasypt</artifactId>
      <version>1.9.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.javassist</groupId>
      <artifactId>javassist</artifactId>
      <version>3.23.2-GA</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Mozilla Public License 1.1</name>
          <url>http://www.mozilla.org/MPL/MPL-1.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jberet</groupId>
      <artifactId>jberet-core</artifactId>
      <version>1.3.7.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 1.0</name>
          <url>http://repository.jboss.org/licenses/epl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-ejb-client</artifactId>
      <version>4.0.33.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-iiop-client</artifactId>
      <version>1.0.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-transaction-spi</artifactId>
      <version>7.6.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.activemq.artemis.integration</groupId>
      <artifactId>artemis-wildfly-integration</artifactId>
      <version>1.0.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ejb3</groupId>
      <artifactId>jboss-ejb3-ext-api</artifactId>
      <version>2.3.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v3.0 only</name>
          <url>http://www.gnu.org/licenses/lgpl-3.0-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.genericjms</groupId>
      <artifactId>generic-jms-ra-jar</artifactId>
      <version>2.0.8.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.hal</groupId>
      <artifactId>hal-console</artifactId>
      <version>3.2.11.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-common-api</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-common-impl</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-common-spi</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-core-api</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-core-impl</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-deployers-common</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-jdbc</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ironjacamar</groupId>
      <artifactId>ironjacamar-validator</artifactId>
      <version>1.4.23.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.metadata</groupId>
      <artifactId>jboss-metadata-appclient</artifactId>
      <version>13.0.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.metadata</groupId>
      <artifactId>jboss-metadata-ejb</artifactId>
      <version>13.0.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.mod_cluster</groupId>
      <artifactId>mod_cluster-container-spi</artifactId>
      <version>1.4.1.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v3.0 or later</name>
          <url>https://spdx.org/licenses/LGPL-3.0+.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.mod_cluster</groupId>
      <artifactId>mod_cluster-core</artifactId>
      <version>1.4.1.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v3.0 or later</name>
          <url>https://spdx.org/licenses/LGPL-3.0+.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.mod_cluster</groupId>
      <artifactId>mod_cluster-load-spi</artifactId>
      <version>1.4.1.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v3.0 or later</name>
          <url>https://spdx.org/licenses/LGPL-3.0+.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana</groupId>
      <artifactId>jbosstxbridge</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.compensations</groupId>
      <artifactId>compensations</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.jts</groupId>
      <artifactId>narayana-jts-idlj</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.jts</groupId>
      <artifactId>narayana-jts-integration</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.rts</groupId>
      <artifactId>restat-api</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.rts</groupId>
      <artifactId>restat-bridge</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.rts</groupId>
      <artifactId>restat-integration</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.rts</groupId>
      <artifactId>restat-util</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.txframework</groupId>
      <artifactId>txframework</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.narayana.xts</groupId>
      <artifactId>jbossxts</artifactId>
      <version>5.10.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.openjdk-orb</groupId>
      <artifactId>openjdk-orb</artifactId>
      <version>8.1.4.Final</version>
      <licenses>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>jose-jwt</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-atom-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-cdi</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-client</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-client-microprofile</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-crypto</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jackson-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jackson2-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jaxb-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jaxrs</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jettison-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-jsapi</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-json-binding-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-json-p-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-multipart-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-rxjava2</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-spring</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-validator-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.resteasy</groupId>
      <artifactId>resteasy-yaml-provider</artifactId>
      <version>3.13.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.seam.integration</groupId>
      <artifactId>jboss-seam-int-jbossas</artifactId>
      <version>7.0.0.GA</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.security</groupId>
      <artifactId>jbossxacml</artifactId>
      <version>2.0.8.Final</version>
      <licenses>
        <license>
          <name>BSD 3-Clause No Nuclear License</name>
          <url>https://spdx.org/licenses/BSD-3-Clause-No-Nuclear-License.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.batch</groupId>
      <artifactId>jboss-batch-api_1.0_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.ejb</groupId>
      <artifactId>jboss-ejb-api_3.2_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.faces</groupId>
      <artifactId>jboss-jsf-api_2.3_spec</artifactId>
      <version>3.0.0.SP04</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.jms</groupId>
      <artifactId>jboss-jms-api_2.0_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.management.j2ee</groupId>
      <artifactId>jboss-j2eemgmt-api_1.1_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.resource</groupId>
      <artifactId>jboss-connector-api_1.7_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.ws.rs</groupId>
      <artifactId>jboss-jaxrs-api_2.1_spec</artifactId>
      <version>2.0.1.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.bind</groupId>
      <artifactId>jboss-jaxb-api_2.3_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.rpc</groupId>
      <artifactId>jboss-jaxrpc-api_1.1_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.soap</groupId>
      <artifactId>jboss-saaj-api_1.4_spec</artifactId>
      <version>1.0.2.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.xml.ws</groupId>
      <artifactId>jboss-jaxws-api_2.3_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld</groupId>
      <artifactId>weld-api</artifactId>
      <version>3.1.SP3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld</groupId>
      <artifactId>weld-core-impl</artifactId>
      <version>3.1.5.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld</groupId>
      <artifactId>weld-spi</artifactId>
      <version>3.1.SP3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld.module</groupId>
      <artifactId>weld-ejb</artifactId>
      <version>3.1.5.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld.module</groupId>
      <artifactId>weld-jsf</artifactId>
      <version>3.1.5.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld.module</groupId>
      <artifactId>weld-jta</artifactId>
      <version>3.1.5.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld.module</groupId>
      <artifactId>weld-web</artifactId>
      <version>3.1.5.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.weld.probe</groupId>
      <artifactId>weld-probe-core</artifactId>
      <version>3.1.5.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws</groupId>
      <artifactId>jbossws-api</artifactId>
      <version>1.1.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws</groupId>
      <artifactId>jbossws-common</artifactId>
      <version>3.3.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws</groupId>
      <artifactId>jbossws-common-tools</artifactId>
      <version>1.3.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws</groupId>
      <artifactId>jbossws-spi</artifactId>
      <version>3.3.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.cxf</groupId>
      <artifactId>jbossws-cxf-client</artifactId>
      <version>5.4.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.cxf</groupId>
      <artifactId>jbossws-cxf-factories</artifactId>
      <version>5.4.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.cxf</groupId>
      <artifactId>jbossws-cxf-jaspi</artifactId>
      <version>5.4.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.cxf</groupId>
      <artifactId>jbossws-cxf-resources</artifactId>
      <version>5.4.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.cxf</groupId>
      <artifactId>jbossws-cxf-server</artifactId>
      <version>5.4.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.cxf</groupId>
      <artifactId>jbossws-cxf-transports-udp</artifactId>
      <version>5.4.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.cxf</groupId>
      <artifactId>jbossws-cxf-transports-undertow</artifactId>
      <version>5.4.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.ws.projects</groupId>
      <artifactId>jaxws-undertow-httpspi</artifactId>
      <version>1.0.1.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.xnio.netty</groupId>
      <artifactId>netty-xnio-transport</artifactId>
      <version>0.1.9.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jctools</groupId>
      <artifactId>jctools-core</artifactId>
      <version>2.1.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jgroups</groupId>
      <artifactId>jgroups</artifactId>
      <version>4.2.5.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jgroups.azure</groupId>
      <artifactId>jgroups-azure</artifactId>
      <version>1.3.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jgroups.kubernetes</groupId>
      <artifactId>jgroups-kubernetes</artifactId>
      <version>1.0.15.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.8.3</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>The JSoup MIT License</name>
          <url>https://jsoup.org/license.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jvnet.staxex</groupId>
      <artifactId>stax-ex</artifactId>
      <version>1.7.8</version>
      <licenses>
        <license>
          <name>Common Development and Distribution License 1.1</name>
          <url>https://javaee.github.io/glassfish/LICENSE</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-core</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-profile-api</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-saml-api</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-saml-impl</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-security-api</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-security-impl</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-soap-api</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-xacml-api</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-xacml-impl</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-xacml-saml-api</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-xacml-saml-impl</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-xmlsec-api</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.opensaml</groupId>
      <artifactId>opensaml-xmlsec-impl</artifactId>
      <version>3.3.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm</artifactId>
      <version>7.1</version>
      <licenses>
        <license>
          <name>BSD 3-Clause "New" or "Revised" License</name>
          <url>http://www.opensource.org/licenses/BSD-3-Clause</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.ow2.asm</groupId>
      <artifactId>asm-util</artifactId>
      <version>7.1</version>
      <licenses>
        <license>
          <name>BSD 3-Clause "New" or "Revised" License</name>
          <url>http://www.opensource.org/licenses/BSD-3-Clause</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-api</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-common</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-config</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-federation</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-idm-api</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-idm-impl</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-idm-simple-schema</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink</groupId>
      <artifactId>picketlink-impl</artifactId>
      <version>2.5.5.SP12-redhat-00009</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketlink.distribution</groupId>
      <artifactId>picketlink-wildfly8</artifactId>
      <version>2.5.5.SP12-redhat-00012</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.reactivestreams</groupId>
      <artifactId>reactive-streams</artifactId>
      <version>1.0.3</version>
      <licenses>
        <license>
          <name>Creative Commons Zero v1.0 Universal</name>
          <url>http://creativecommons.org/publicdomain/zero/1.0/legalcode</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>jipijapa-eclipselink</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>jipijapa-hibernate4-1</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>jipijapa-hibernate4-3</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>jipijapa-hibernate5</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>jipijapa-hibernate5-3</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>jipijapa-openjpa</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>jipijapa-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-appclient</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-batch-jberet</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-bean-validation</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-client-all</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-api</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-common</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-ee-cache</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-ee-hotrod</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-ee-infinispan</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-ee-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-ejb-infinispan</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-ejb-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-infinispan-client</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-infinispan-extension</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-infinispan-marshalling</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-infinispan-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-jgroups-api</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-jgroups-extension</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-jgroups-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-marshalling-api</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-marshalling-jboss</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-marshalling-protostream</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-marshalling-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-server</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-service</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-singleton-api</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-singleton-extension</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-api</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-cache</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-container</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-extension</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-hotrod</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-infinispan</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-undertow</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-cmp</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-configadmin</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-connector</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-datasources-agroal</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-ee-security</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-ejb3</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-iiop-openjdk</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jacorb</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jaxr</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jaxrs</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jdr</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jpa</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jsf</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jsf-injection</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-jsr77</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-mail</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-messaging</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-messaging-activemq</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-config-smallrye</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-health-smallrye</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-metrics-smallrye</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-opentracing-extension</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-opentracing-smallrye</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-mod_cluster-extension</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-mod_cluster-undertow</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-naming-client</artifactId>
      <version>1.0.13.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-ormtransformer</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-picketlink</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-pojo</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-rts</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-sar</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-security-api</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-security-vault-tool</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-system-jmx</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-transactions</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-web</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-webservices-server-integration</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld-bean-validation</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld-common</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld-ejb</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld-jpa</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld-spi</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld-transactions</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-weld-webservices</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-xts</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-cli</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-version</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.discovery</groupId>
      <artifactId>wildfly-discovery-client</artifactId>
      <version>1.2.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.transaction</groupId>
      <artifactId>wildfly-transaction-client</artifactId>
      <version>1.1.13.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.wildfly-http-client</groupId>
      <artifactId>wildfly-http-client-common</artifactId>
      <version>1.1.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.wildfly-http-client</groupId>
      <artifactId>wildfly-http-ejb-client</artifactId>
      <version>1.1.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.wildfly-http-client</groupId>
      <artifactId>wildfly-http-naming-client</artifactId>
      <version>1.1.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.wildfly-http-client</groupId>
      <artifactId>wildfly-http-transaction-client</artifactId>
      <version>1.1.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.yaml</groupId>
      <artifactId>snakeyaml</artifactId>
      <version>1.26</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>wsdl4j</groupId>
      <artifactId>wsdl4j</artifactId>
      <version>1.6.3</version>
      <licenses>
        <license>
          <name>Common Public License 1.0</name>
          <url>http://www.eclipse.org/legal/cpl-v10.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>xml-resolver</groupId>
      <artifactId>xml-resolver</artifactId>
      <version>1.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>full-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.sun.activation</groupId>
      <artifactId>jakarta.activation</artifactId>
      <version>1.2.1</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.undertow</groupId>
      <artifactId>undertow-servlet</artifactId>
      <version>2.2.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.undertow</groupId>
      <artifactId>undertow-websockets-jsr</artifactId>
      <version>2.2.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.undertow.jastow</groupId>
      <artifactId>jastow</artifactId>
      <version>2.0.9.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jakarta.enterprise</groupId>
      <artifactId>jakarta.enterprise.cdi-api</artifactId>
      <version>2.0.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jakarta.inject</groupId>
      <artifactId>jakarta.inject-api</artifactId>
      <version>1.0.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jakarta.json.bind</groupId>
      <artifactId>jakarta.json.bind-api</artifactId>
      <version>1.0.2</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jakarta.validation</groupId>
      <artifactId>jakarta.validation-api</artifactId>
      <version>2.0.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.taglibs</groupId>
      <artifactId>taglibs-standard-compat</artifactId>
      <version>1.2.6-RC1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.taglibs</groupId>
      <artifactId>taglibs-standard-impl</artifactId>
      <version>1.2.6-RC1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.taglibs</groupId>
      <artifactId>taglibs-standard-spec</artifactId>
      <version>1.2.6-RC1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse</groupId>
      <artifactId>yasson</artifactId>
      <version>1.0.5</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Eclipse Public License 1.0</name>
          <url>http://repository.jboss.org/licenses/epl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jdt.core.compiler</groupId>
      <artifactId>ecj</artifactId>
      <version>4.6.1</version>
      <licenses>
        <license>
          <name>Eclipse Public License 1.0</name>
          <url>http://repository.jboss.org/licenses/epl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish</groupId>
      <artifactId>jakarta.el</artifactId>
      <version>3.0.3.jbossorg-2</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish</groupId>
      <artifactId>jakarta.enterprise.concurrent</artifactId>
      <version>1.1.1</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.common</groupId>
      <artifactId>jboss-common-beans</artifactId>
      <version>2.0.1.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.metadata</groupId>
      <artifactId>jboss-metadata-common</artifactId>
      <version>13.0.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.metadata</groupId>
      <artifactId>jboss-metadata-ear</artifactId>
      <version>13.0.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.metadata</groupId>
      <artifactId>jboss-metadata-web</artifactId>
      <version>13.0.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.security</groupId>
      <artifactId>jboss-negotiation-common</artifactId>
      <version>3.0.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.security</groupId>
      <artifactId>jboss-negotiation-extras</artifactId>
      <version>3.0.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.security</groupId>
      <artifactId>jboss-negotiation-ntlm</artifactId>
      <version>3.0.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.security</groupId>
      <artifactId>jboss-negotiation-spnego</artifactId>
      <version>3.0.6.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.annotation</groupId>
      <artifactId>jboss-annotations-api_1.3_spec</artifactId>
      <version>2.0.1.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.el</groupId>
      <artifactId>jboss-el-api_3.0_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.enterprise.concurrent</groupId>
      <artifactId>jboss-concurrency-api_1.0_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.servlet</groupId>
      <artifactId>jboss-servlet-api_4.0_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.servlet.jsp</groupId>
      <artifactId>jboss-jsp-api_2.3_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.transaction</groupId>
      <artifactId>jboss-transaction-api_1.3_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.websocket</groupId>
      <artifactId>jboss-websocket-api_1.1_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License v2.0 only, with Classpath exception</name>
          <url>https://fedoraproject.org/wiki/Licensing/GPL_Classpath_Exception</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketbox</groupId>
      <artifactId>picketbox</artifactId>
      <version>5.0.3.Final-redhat-00006</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketbox</groupId>
      <artifactId>picketbox-commons</artifactId>
      <version>1.0.0.final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.picketbox</groupId>
      <artifactId>picketbox-infinispan</artifactId>
      <version>5.0.3.Final-redhat-00006</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-common</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-service</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-clustering-web-container</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-ee</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-naming</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-naming-client</artifactId>
      <version>1.0.13.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-security</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-security-integration</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-security-plugins</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-undertow</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-web-common</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-elytron-integration</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-event-logger</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security.elytron-web</groupId>
      <artifactId>undertow-server-servlet</artifactId>
      <version>1.8.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.wildfly-http-client</groupId>
      <artifactId>wildfly-http-client-common</artifactId>
      <version>1.1.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.wildfly-http-client</groupId>
      <artifactId>wildfly-http-naming-client</artifactId>
      <version>1.1.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>xalan</groupId>
      <artifactId>serializer</artifactId>
      <version>2.7.1.jbossorg-4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>xalan</groupId>
      <artifactId>xalan</artifactId>
      <version>2.7.1.jbossorg-4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>servlet-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.woodstox</groupId>
      <artifactId>woodstox-core</artifactId>
      <version>6.0.3</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.googlecode.javaewah</groupId>
      <artifactId>JavaEWAH</artifactId>
      <version>1.1.7</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.undertow</groupId>
      <artifactId>undertow-core</artifactId>
      <version>2.2.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>jakarta.json</groupId>
      <artifactId>jakarta.json-api</artifactId>
      <version>1.1.6</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.aesh</groupId>
      <artifactId>aesh</artifactId>
      <version>2.4</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.aesh</groupId>
      <artifactId>aesh-extensions</artifactId>
      <version>1.8</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.aesh</groupId>
      <artifactId>readline</artifactId>
      <version>2.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpclient</artifactId>
      <version>4.5.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.httpcomponents</groupId>
      <artifactId>httpcore</artifactId>
      <version>4.4.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.sshd</groupId>
      <artifactId>sshd-common</artifactId>
      <version>2.4.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.apache.sshd</groupId>
      <artifactId>sshd-core</artifactId>
      <version>2.4.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpg-jdk15on</artifactId>
      <version>1.65</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcpkix-jdk15on</artifactId>
      <version>1.65</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.bouncycastle</groupId>
      <artifactId>bcprov-jdk15on</artifactId>
      <version>1.65</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.codehaus.woodstox</groupId>
      <artifactId>stax2-api</artifactId>
      <version>4.2.1</version>
      <licenses>
        <license>
          <name>The BSD License</name>
          <url>http://repository.jboss.org/licenses/bsd.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit</artifactId>
      <version>5.9.0.202009080501-r</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.jgit</groupId>
      <artifactId>org.eclipse.jgit.ssh.apache</artifactId>
      <version>5.9.0.202009080501-r</version>
      <licenses>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.fusesource.jansi</groupId>
      <artifactId>jansi</artifactId>
      <version>1.18</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.glassfish</groupId>
      <artifactId>jakarta.json</artifactId>
      <version>1.1.6</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Eclipse Distribution License, Version 1.0</name>
          <url>http://repository.jboss.org/licenses/edl-1.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jandex</artifactId>
      <version>2.1.3.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-dmr</artifactId>
      <version>1.5.1.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>jboss-vfs</artifactId>
      <version>3.2.15.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss</groupId>
      <artifactId>staxmapper</artifactId>
      <version>1.3.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.classfilewriter</groupId>
      <artifactId>jboss-classfilewriter</artifactId>
      <version>1.2.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.invocation</groupId>
      <artifactId>jboss-invocation</artifactId>
      <version>1.6.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.logging</groupId>
      <artifactId>commons-logging-jboss-logging</artifactId>
      <version>1.0.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.logging</groupId>
      <artifactId>jboss-logging</artifactId>
      <version>3.4.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.logging</groupId>
      <artifactId>jul-to-slf4j-stub</artifactId>
      <version>1.0.1.Final</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.logmanager</groupId>
      <artifactId>jboss-logmanager</artifactId>
      <version>2.1.17.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.logmanager</groupId>
      <artifactId>log4j-jboss-logmanager</artifactId>
      <version>1.2.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.marshalling</groupId>
      <artifactId>jboss-marshalling</artifactId>
      <version>2.0.9.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.marshalling</groupId>
      <artifactId>jboss-marshalling-river</artifactId>
      <version>2.0.9.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.modules</groupId>
      <artifactId>jboss-modules</artifactId>
      <version>1.10.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Indiana University Extreme! Lab Software License 1.1.1</name>
          <url>https://enterprise.dejacode.com/licenses/public/indiana-extreme/?_list_filters=q%3Dindiana%2Bextreme#license-text</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.msc</groupId>
      <artifactId>jboss-msc</artifactId>
      <version>1.4.12.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.remoting</groupId>
      <artifactId>jboss-remoting</artifactId>
      <version>5.0.19.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.remotingjmx</groupId>
      <artifactId>remoting-jmx</artifactId>
      <version>3.0.4.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.slf4j</groupId>
      <artifactId>slf4j-jboss-logmanager</artifactId>
      <version>1.0.4.GA</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.interceptor</groupId>
      <artifactId>jboss-interceptors-api_1.2_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.security.auth.message</groupId>
      <artifactId>jboss-jaspi-api_1.1_spec</artifactId>
      <version>2.0.1.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.spec.javax.security.jacc</groupId>
      <artifactId>jboss-jacc-api_1.5_spec</artifactId>
      <version>2.0.0.Final</version>
      <licenses>
        <license>
          <name>Eclipse Public License 2.0</name>
          <url>http://www.eclipse.org/legal/epl-v20.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>GNU General Public License, Version 2 with the Classpath Exception</name>
          <url>http://repository.jboss.org/licenses/gpl-2.0-ce.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.stdio</groupId>
      <artifactId>jboss-stdio</artifactId>
      <version>1.1.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.threads</groupId>
      <artifactId>jboss-threads</artifactId>
      <version>2.4.0.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 only</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.xnio</groupId>
      <artifactId>xnio-api</artifactId>
      <version>3.8.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.jboss.xnio</groupId>
      <artifactId>xnio-nio</artifactId>
      <version>3.8.2.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.projectodd.vdx</groupId>
      <artifactId>vdx-core</artifactId>
      <version>1.1.6</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.projectodd.vdx</groupId>
      <artifactId>vdx-wildfly</artifactId>
      <version>1.1.6</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>1.7.30</version>
      <licenses>
        <license>
          <name>MIT License</name>
          <url>http://www.opensource.org/licenses/MIT</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.client</groupId>
      <artifactId>wildfly-client-config</artifactId>
      <version>1.0.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.common</groupId>
      <artifactId>wildfly-common</artifactId>
      <version>1.5.4.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-cli</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-controller</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-controller-client</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-core-feature-pack-common</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://repository.jboss.org/licenses/apache-2.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-core-feature-pack-ee-8-api</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://repository.jboss.org/licenses/apache-2.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-core-management-client</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-core-management-subsystem</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-core-security</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-core-security-api</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-deployment-repository</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-deployment-scanner</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-discovery</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-domain-http-error-context</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-domain-http-interface</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-domain-management</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-elytron-integration</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-embedded</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-event-logger</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-host-controller</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-io</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-jar-runtime</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-jmx</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-launcher</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-logging</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-management-client-content</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-network</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-patching</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-platform-mbean</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-process-controller</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-protocol</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-remoting</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-request-controller</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-security-manager</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-server</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-threads</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.core</groupId>
      <artifactId>wildfly-version</artifactId>
      <version>13.0.3.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://www.gnu.org/licenses/old-licenses/lgpl-2.1-standalone.html</url>
          <distribution>repo</distribution>
        </license>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.discovery</groupId>
      <artifactId>wildfly-discovery-client</artifactId>
      <version>1.2.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-java</artifactId>
      <version>2.1.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-linux-i386</artifactId>
      <version>2.1.0.SP01</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-linux-s390x</artifactId>
      <version>2.1.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-linux-x86_64</artifactId>
      <version>2.1.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-macosx-x86_64</artifactId>
      <version>2.1.0.SP01</version>
      <licenses>
        <license>
          <name>Apache License Version 2.0</name>
          <url>http://repository.jboss.org/licenses/apache-2.0.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-solaris-x86_64</artifactId>
      <version>2.1.0.SP01</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-windows-i386</artifactId>
      <version>2.1.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.openssl</groupId>
      <artifactId>wildfly-openssl-windows-x86_64</artifactId>
      <version>2.1.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-asn1</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-audit</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server-deprecated</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server-http</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-server-sasl</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-auth-util</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-base</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-client</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential-source-deprecated</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential-source-impl</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-credential-store</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-digest</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-basic</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-bearer</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-cert</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-deprecated</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-digest</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-external</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-form</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-spnego</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-sso</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-http-util</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-jacc</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-jaspi</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-json-util</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-keystore</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-digest</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-gssapi</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-http</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-oauth2</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-mechanism-scram</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-password-impl</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-permission</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-provider-util</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm-jdbc</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm-ldap</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-realm-token</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-anonymous</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-auth-util</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-deprecated</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-digest</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-entity</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-external</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-gs2</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-gssapi</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-localuser</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-oauth2</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-otp</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-plain</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-sasl-scram</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-security-manager</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-security-manager-action</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-ssl</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-tool</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-util</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-cert</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-cert-acme</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-cert-util</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-deprecated</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-x500-principal</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security.elytron-web</groupId>
      <artifactId>undertow-server</artifactId>
      <version>1.8.0.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>xerces</groupId>
      <artifactId>xercesImpl</artifactId>
      <version>2.12.0.SP03</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>core-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-yaml</artifactId>
      <version>2.10.5</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-fault-tolerance</artifactId>
      <version>4.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-fault-tolerance-api</artifactId>
      <version>4.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-fault-tolerance-core</artifactId>
      <version>4.3.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-jwt</artifactId>
      <version>2.0.13</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-open-api-core</artifactId>
      <version>2.0.9</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>io.smallrye</groupId>
      <artifactId>smallrye-open-api-jaxrs</artifactId>
      <version>2.0.9</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.bitbucket.b_c</groupId>
      <artifactId>jose4j</artifactId>
      <version>0.7.0</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.fault-tolerance</groupId>
      <artifactId>microprofile-fault-tolerance-api</artifactId>
      <version>2.1.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.jwt</groupId>
      <artifactId>microprofile-jwt-auth-api</artifactId>
      <version>1.1.1</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.eclipse.microprofile.openapi</groupId>
      <artifactId>microprofile-openapi-api</artifactId>
      <version>1.1.2</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-fault-tolerance-smallrye-executor</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-fault-tolerance-smallrye-extension</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-jwt-smallrye</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly</groupId>
      <artifactId>wildfly-microprofile-openapi-smallrye</artifactId>
      <version>21.0.2.Final</version>
      <licenses>
        <license>
          <name>GNU Lesser General Public License v2.1 or later</name>
          <url>http://repository.jboss.org/licenses/lgpl-2.1.txt</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
    <dependency>
      <groupId>org.wildfly.security</groupId>
      <artifactId>wildfly-elytron-jwt</artifactId>
      <version>1.13.1.Final</version>
      <licenses>
        <license>
          <name>Apache License 2.0</name>
          <url>http://www.apache.org/licenses/LICENSE-2.0</url>
          <distribution>repo</distribution>
        </license>
      </licenses>
      <source>microprofile-feature-pack-licenses.xml</source>
    </dependency>
  </dependencies>
</licenseSummary>