<?xml version='1.0' encoding='UTF-8'?>

<server xmlns="urn:jboss:domain:14.0">
    <extensions>
        <extension module="org.jboss.as.clustering.infinispan"/>
        <extension module="org.jboss.as.connector"/>
        <extension module="org.jboss.as.deployment-scanner"/>
        <extension module="org.jboss.as.ee"/>
        <extension module="org.jboss.as.ejb3"/>
        <extension module="org.jboss.as.jaxrs"/>
        <extension module="org.jboss.as.jdr"/>
        <extension module="org.jboss.as.jmx"/>
        <extension module="org.jboss.as.jpa"/>
        <extension module="org.jboss.as.jsf"/>
        <extension module="org.jboss.as.jsr77"/>
        <extension module="org.jboss.as.logging"/>
        <extension module="org.jboss.as.mail"/>
        <extension module="org.jboss.as.naming"/>
        <extension module="org.jboss.as.pojo"/>
        <extension module="org.jboss.as.remoting"/>
        <extension module="org.jboss.as.sar"/>
        <extension module="org.jboss.as.security"/>
        <extension module="org.jboss.as.transactions"/>
        <extension module="org.jboss.as.webservices"/>
        <extension module="org.jboss.as.weld"/>
        <extension module="org.wildfly.extension.bean-validation"/>
        <extension module="org.wildfly.extension.clustering.web"/>
        <extension module="org.wildfly.extension.core-management"/>
        <extension module="org.wildfly.extension.discovery"/>
        <extension module="org.wildfly.extension.ee-security"/>
        <extension module="org.wildfly.extension.elytron"/>
        <extension module="org.wildfly.extension.io"/>
        <extension module="org.wildfly.extension.messaging-activemq"/>
        <extension module="org.wildfly.extension.microprofile.config-smallrye"/>
        <extension module="org.wildfly.extension.microprofile.health-smallrye"/>
        <extension module="org.wildfly.extension.microprofile.metrics-smallrye"/>
        <extension module="org.wildfly.extension.microprofile.opentracing-smallrye"/>
        <extension module="org.wildfly.extension.picketlink"/>
        <extension module="org.wildfly.extension.request-controller"/>
        <extension module="org.wildfly.extension.security.manager"/>
        <extension module="org.wildfly.extension.undertow"/>
        <extension module="org.wildfly.iiop-openjdk"/>
    </extensions>
    <management>
        <security-realms>
            <security-realm name="ManagementRealm">
                <authentication>
                    <local default-user="$local" skip-group-loading="true"/>
                    <properties path="mgmt-users.properties" relative-to="jboss.server.config.dir"/>
                </authentication>
                <authorization map-groups-to-roles="false">
                    <properties path="mgmt-groups.properties" relative-to="jboss.server.config.dir"/>
                </authorization>
            </security-realm>
            <security-realm name="ApplicationRealm">
                <server-identities>
                    <ssl>
                        <keystore path="application.keystore" relative-to="jboss.server.config.dir" keystore-password="password" alias="server" key-password="password" generate-self-signed-certificate-host="localhost"/>
                    </ssl>
                </server-identities>
                <authentication>
                    <local default-user="$local" allowed-users="*" skip-group-loading="true"/>
                    <properties path="application-users.properties" relative-to="jboss.server.config.dir"/>
                </authentication>
                <authorization>
                    <properties path="application-roles.properties" relative-to="jboss.server.config.dir"/>
                </authorization>
            </security-realm>
        </security-realms>
        <audit-log>
            <formatters>
                <json-formatter name="json-formatter"/>
            </formatters>
            <handlers>
                <file-handler name="file" formatter="json-formatter" path="audit-log.log" relative-to="jboss.server.data.dir"/>
            </handlers>
            <logger log-boot="true" log-read-only="false" enabled="false">
                <handlers>
                    <handler name="file"/>
                </handlers>
            </logger>
        </audit-log>
        <management-interfaces>
            <http-interface security-realm="ManagementRealm">
                <http-upgrade enabled="true"/>
                <socket-binding http="management-http"/>
            </http-interface>
        </management-interfaces>
        <access-control provider="simple">
            <role-mapping>
                <role name="SuperUser">
                    <include>
                        <user name="$local"/>
                    </include>
                </role>
            </role-mapping>
        </access-control>
    </management>
    <profile>
        <subsystem xmlns="urn:jboss:domain:logging:8.0">
            <console-handler name="CONSOLE">
                <level name="INFO"/>
                <formatter>
                    <named-formatter name="COLOR-PATTERN"/>
                </formatter>
            </console-handler>
            <periodic-rotating-file-handler name="FILE" autoflush="true">
                <formatter>
                    <named-formatter name="PATTERN"/>
                </formatter>
                <file relative-to="jboss.server.log.dir" path="server.log"/>
                <suffix value=".yyyy-MM-dd"/>
                <append value="true"/>
            </periodic-rotating-file-handler>
            <logger category="com.arjuna">
                <level name="WARN"/>
            </logger>
            <logger category="io.jaegertracing.Configuration">
                <level name="WARN"/>
            </logger>
            <logger category="org.jboss.as.config">
                <level name="DEBUG"/>
            </logger>
            <logger category="sun.rmi">
                <level name="WARN"/>
            </logger>
            <root-logger>
                <level name="INFO"/>
                <handlers>
                    <handler name="CONSOLE"/>
                    <handler name="FILE"/>
                </handlers>
            </root-logger>
            <formatter name="PATTERN">
                <pattern-formatter pattern="%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n"/>
            </formatter>
            <formatter name="COLOR-PATTERN">
                <pattern-formatter pattern="%K{level}%d{HH:mm:ss,SSS} %-5p [%c] (%t) %s%e%n"/>
            </formatter>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:bean-validation:1.0"/>
        <subsystem xmlns="urn:jboss:domain:core-management:1.0"/>
        <subsystem xmlns="urn:jboss:domain:datasources:6.0">
            <datasources>
                <datasource jndi-name="java:jboss/datasources/ExampleDS" pool-name="ExampleDS" enabled="true" use-java-context="true" statistics-enabled="${wildfly.datasources.statistics-enabled:${wildfly.statistics-enabled:false}}">
                    <connection-url>jdbc:h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE</connection-url>
                    <driver>h2</driver>
                    <security>
                        <user-name>sa</user-name>
                        <password>sa</password>
                    </security>
                </datasource>
                <drivers>
                    <driver name="h2" module="com.h2database.h2">
                        <xa-datasource-class>org.h2.jdbcx.JdbcDataSource</xa-datasource-class>
                    </driver>
                </drivers>
            </datasources>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:deployment-scanner:2.0">
            <deployment-scanner path="deployments" relative-to="jboss.server.base.dir" scan-interval="5000" runtime-failure-causes-rollback="${jboss.deployment.scanner.rollback.on.failure:false}"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:discovery:1.0"/>
        <subsystem xmlns="urn:jboss:domain:distributable-web:2.0" default-session-management="default" default-single-sign-on-management="default">
            <infinispan-session-management name="default" cache-container="web" granularity="SESSION">
                <primary-owner-affinity/>
            </infinispan-session-management>
            <infinispan-single-sign-on-management name="default" cache-container="web" cache="sso"/>
            <infinispan-routing cache-container="web" cache="routing"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:ee:5.0">
            <spec-descriptor-property-replacement>false</spec-descriptor-property-replacement>
            <concurrent>
                <context-services>
                    <context-service name="default" jndi-name="java:jboss/ee/concurrency/context/default" use-transaction-setup-provider="true"/>
                </context-services>
                <managed-thread-factories>
                    <managed-thread-factory name="default" jndi-name="java:jboss/ee/concurrency/factory/default" context-service="default"/>
                </managed-thread-factories>
                <managed-executor-services>
                    <managed-executor-service name="default" jndi-name="java:jboss/ee/concurrency/executor/default" context-service="default" hung-task-threshold="60000" keepalive-time="5000"/>
                </managed-executor-services>
                <managed-scheduled-executor-services>
                    <managed-scheduled-executor-service name="default" jndi-name="java:jboss/ee/concurrency/scheduler/default" context-service="default" hung-task-threshold="60000" keepalive-time="3000"/>
                </managed-scheduled-executor-services>
            </concurrent>
            <default-bindings context-service="java:jboss/ee/concurrency/context/default" datasource="java:jboss/datasources/ExampleDS" jms-connection-factory="java:jboss/DefaultJMSConnectionFactory" managed-executor-service="java:jboss/ee/concurrency/executor/default" managed-scheduled-executor-service="java:jboss/ee/concurrency/scheduler/default" managed-thread-factory="java:jboss/ee/concurrency/factory/default"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:ee-security:1.0"/>
        <subsystem xmlns="urn:jboss:domain:ejb3:8.0">
            <session-bean>
                <stateless>
                    <bean-instance-pool-ref pool-name="slsb-strict-max-pool"/>
                </stateless>
                <stateful default-access-timeout="5000" cache-ref="simple" passivation-disabled-cache-ref="simple"/>
                <singleton default-access-timeout="5000"/>
            </session-bean>
            <mdb>
                <resource-adapter-ref resource-adapter-name="${ejb.resource-adapter-name:activemq-ra.rar}"/>
                <bean-instance-pool-ref pool-name="mdb-strict-max-pool"/>
            </mdb>
            <pools>
                <bean-instance-pools>
                    <strict-max-pool name="mdb-strict-max-pool" derive-size="from-cpu-count" instance-acquisition-timeout="5" instance-acquisition-timeout-unit="MINUTES"/>
                    <strict-max-pool name="slsb-strict-max-pool" derive-size="from-worker-pools" instance-acquisition-timeout="5" instance-acquisition-timeout-unit="MINUTES"/>
                </bean-instance-pools>
            </pools>
            <caches>
                <cache name="simple"/>
                <cache name="distributable" passivation-store-ref="infinispan" aliases="passivating clustered"/>
            </caches>
            <passivation-stores>
                <passivation-store name="infinispan" cache-container="ejb" max-size="10000"/>
            </passivation-stores>
            <async thread-pool-name="default"/>
            <timer-service thread-pool-name="default" default-data-store="default-file-store">
                <data-stores>
                    <file-data-store name="default-file-store" path="timer-service-data" relative-to="jboss.server.data.dir"/>
                </data-stores>
            </timer-service>
            <remote cluster="ejb" connectors="http-remoting-connector" thread-pool-name="default">
                <channel-creation-options>
                    <option name="MAX_OUTBOUND_MESSAGES" value="1234" type="remoting"/>
                </channel-creation-options>
            </remote>
            <thread-pools>
                <thread-pool name="default">
                    <max-threads count="10"/>
                    <keepalive-time time="60" unit="seconds"/>
                </thread-pool>
            </thread-pools>
            <iiop enable-by-default="false" use-qualified-name="false"/>
            <default-security-domain value="other"/>
            <default-missing-method-permissions-deny-access value="true"/>
            <statistics enabled="${wildfly.ejb3.statistics-enabled:${wildfly.statistics-enabled:false}}"/>
            <log-system-exceptions value="true"/>
        </subsystem>
        <subsystem xmlns="urn:wildfly:elytron:11.0" final-providers="combined-providers" disallowed-providers="OracleUcrypto">
            <providers>
                <aggregate-providers name="combined-providers">
                    <providers name="elytron"/>
                    <providers name="openssl"/>
                </aggregate-providers>
                <provider-loader name="elytron" module="org.wildfly.security.elytron"/>
                <provider-loader name="openssl" module="org.wildfly.openssl"/>
            </providers>
            <audit-logging>
                <file-audit-log name="local-audit" path="audit.log" relative-to="jboss.server.log.dir" format="JSON"/>
            </audit-logging>
            <security-domains>
                <security-domain name="ApplicationDomain" default-realm="ApplicationRealm" permission-mapper="default-permission-mapper">
                    <realm name="ApplicationRealm" role-decoder="groups-to-roles"/>
                    <realm name="local"/>
                </security-domain>
                <security-domain name="ManagementDomain" default-realm="ManagementRealm" permission-mapper="default-permission-mapper">
                    <realm name="ManagementRealm" role-decoder="groups-to-roles"/>
                    <realm name="local" role-mapper="super-user-mapper"/>
                </security-domain>
            </security-domains>
            <security-realms>
                <identity-realm name="local" identity="$local"/>
                <properties-realm name="ApplicationRealm">
                    <users-properties path="application-users.properties" relative-to="jboss.server.config.dir" digest-realm-name="ApplicationRealm"/>
                    <groups-properties path="application-roles.properties" relative-to="jboss.server.config.dir"/>
                </properties-realm>
                <properties-realm name="ManagementRealm">
                    <users-properties path="mgmt-users.properties" relative-to="jboss.server.config.dir" digest-realm-name="ManagementRealm"/>
                    <groups-properties path="mgmt-groups.properties" relative-to="jboss.server.config.dir"/>
                </properties-realm>
            </security-realms>
            <mappers>
                <simple-permission-mapper name="default-permission-mapper" mapping-mode="first">
                    <permission-mapping>
                        <principal name="anonymous"/>
                        <permission-set name="default-permissions"/>
                    </permission-mapping>
                    <permission-mapping match-all="true">
                        <permission-set name="login-permission"/>
                        <permission-set name="default-permissions"/>
                    </permission-mapping>
                </simple-permission-mapper>
                <constant-realm-mapper name="local" realm-name="local"/>
                <simple-role-decoder name="groups-to-roles" attribute="groups"/>
                <constant-role-mapper name="super-user-mapper">
                    <role name="SuperUser"/>
                </constant-role-mapper>
            </mappers>
            <permission-sets>
                <permission-set name="login-permission">
                    <permission class-name="org.wildfly.security.auth.permission.LoginPermission"/>
                </permission-set>
                <permission-set name="default-permissions">
                    <permission class-name="org.wildfly.extension.batch.jberet.deployment.BatchPermission" module="org.wildfly.extension.batch.jberet" target-name="*"/>
                    <permission class-name="org.wildfly.transaction.client.RemoteTransactionPermission" module="org.wildfly.transaction.client"/>
                    <permission class-name="org.jboss.ejb.client.RemoteEJBPermission" module="org.jboss.ejb-client"/>
                </permission-set>
            </permission-sets>
            <http>
                <http-authentication-factory name="management-http-authentication" security-domain="ManagementDomain" http-server-mechanism-factory="global">
                    <mechanism-configuration>
                        <mechanism mechanism-name="DIGEST">
                            <mechanism-realm realm-name="ManagementRealm"/>
                        </mechanism>
                    </mechanism-configuration>
                </http-authentication-factory>
                <provider-http-server-mechanism-factory name="global"/>
            </http>
            <sasl>
                <sasl-authentication-factory name="application-sasl-authentication" sasl-server-factory="configured" security-domain="ApplicationDomain">
                    <mechanism-configuration>
                        <mechanism mechanism-name="JBOSS-LOCAL-USER" realm-mapper="local"/>
                        <mechanism mechanism-name="DIGEST-MD5">
                            <mechanism-realm realm-name="ApplicationRealm"/>
                        </mechanism>
                    </mechanism-configuration>
                </sasl-authentication-factory>
                <sasl-authentication-factory name="management-sasl-authentication" sasl-server-factory="configured" security-domain="ManagementDomain">
                    <mechanism-configuration>
                        <mechanism mechanism-name="JBOSS-LOCAL-USER" realm-mapper="local"/>
                        <mechanism mechanism-name="DIGEST-MD5">
                            <mechanism-realm realm-name="ManagementRealm"/>
                        </mechanism>
                    </mechanism-configuration>
                </sasl-authentication-factory>
                <configurable-sasl-server-factory name="configured" sasl-server-factory="elytron">
                    <properties>
                        <property name="wildfly.sasl.local-user.default-user" value="$local"/>
                    </properties>
                </configurable-sasl-server-factory>
                <mechanism-provider-filtering-sasl-server-factory name="elytron" sasl-server-factory="global">
                    <filters>
                        <filter provider-name="WildFlyElytron"/>
                    </filters>
                </mechanism-provider-filtering-sasl-server-factory>
                <provider-sasl-server-factory name="global"/>
            </sasl>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:iiop-openjdk:2.1">
            <orb socket-binding="iiop"/>
            <initializers security="identity" transactions="spec"/>
            <security server-requires-ssl="false" client-requires-ssl="false"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:infinispan:11.0">
            <cache-container name="web" default-cache="passivation" module="org.wildfly.clustering.web.infinispan">
                <local-cache name="passivation">
                    <file-store passivation="true" purge="false"/>
                </local-cache>
                <local-cache name="sso"/>
                <local-cache name="routing"/>
            </cache-container>
            <cache-container name="ejb" aliases="sfsb" default-cache="passivation" module="org.wildfly.clustering.ejb.infinispan">
                <local-cache name="passivation">
                    <file-store passivation="true" purge="false"/>
                </local-cache>
            </cache-container>
            <cache-container name="server" default-cache="default" module="org.wildfly.clustering.server">
                <local-cache name="default"/>
            </cache-container>
            <cache-container name="hibernate" module="org.infinispan.hibernate-cache">
                <local-cache name="entity">
                    <heap-memory size="10000"/>
                    <expiration max-idle="100000"/>
                </local-cache>
                <local-cache name="local-query">
                    <heap-memory size="10000"/>
                    <expiration max-idle="100000"/>
                </local-cache>
                <local-cache name="timestamps"/>
                <local-cache name="pending-puts">
                    <expiration max-idle="60000"/>
                </local-cache>
            </cache-container>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:io:3.0">
            <worker name="default"/>
            <buffer-pool name="default"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:jaxrs:2.0"/>
        <subsystem xmlns="urn:jboss:domain:jca:5.0">
            <archive-validation enabled="true" fail-on-error="true" fail-on-warn="false"/>
            <bean-validation enabled="true"/>
            <default-workmanager>
                <short-running-threads>
                    <core-threads count="50"/>
                    <queue-length count="50"/>
                    <max-threads count="50"/>
                    <keepalive-time time="10" unit="seconds"/>
                </short-running-threads>
                <long-running-threads>
                    <core-threads count="50"/>
                    <queue-length count="50"/>
                    <max-threads count="50"/>
                    <keepalive-time time="10" unit="seconds"/>
                </long-running-threads>
            </default-workmanager>
            <cached-connection-manager/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:jdr:1.0"/>
        <subsystem xmlns="urn:jboss:domain:jmx:1.3">
            <expose-resolved-model/>
            <expose-expression-model/>
            <remoting-connector/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:jpa:1.1">
            <jpa default-datasource="" default-extended-persistence-inheritance="DEEP"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:jsf:1.1"/>
        <subsystem xmlns="urn:jboss:domain:jsr77:1.0"/>
        <subsystem xmlns="urn:jboss:domain:mail:4.0">
            <mail-session name="default" jndi-name="java:jboss/mail/Default">
                <smtp-server outbound-socket-binding-ref="mail-smtp"/>
            </mail-session>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:messaging-activemq:11.0">
            <server name="default">
                <statistics enabled="${wildfly.messaging-activemq.statistics-enabled:${wildfly.statistics-enabled:false}}"/>
                <security-setting name="#">
                    <role name="guest" send="true" consume="true" create-non-durable-queue="true" delete-non-durable-queue="true"/>
                </security-setting>
                <address-setting name="#" dead-letter-address="jms.queue.DLQ" expiry-address="jms.queue.ExpiryQueue" max-size-bytes="10485760" page-size-bytes="2097152" message-counter-history-day-limit="10"/>
                <http-connector name="http-connector" socket-binding="http" endpoint="http-acceptor"/>
                <http-connector name="http-connector-throughput" socket-binding="http" endpoint="http-acceptor-throughput">
                    <param name="batch-delay" value="50"/>
                </http-connector>
                <in-vm-connector name="in-vm" server-id="0">
                    <param name="buffer-pooling" value="false"/>
                </in-vm-connector>
                <http-acceptor name="http-acceptor" http-listener="default"/>
                <http-acceptor name="http-acceptor-throughput" http-listener="default">
                    <param name="batch-delay" value="50"/>
                    <param name="direct-deliver" value="false"/>
                </http-acceptor>
                <in-vm-acceptor name="in-vm" server-id="0">
                    <param name="buffer-pooling" value="false"/>
                </in-vm-acceptor>
                <jms-queue name="ExpiryQueue" entries="java:/jms/queue/ExpiryQueue"/>
                <jms-queue name="DLQ" entries="java:/jms/queue/DLQ"/>
                <connection-factory name="InVmConnectionFactory" entries="java:/ConnectionFactory" connectors="in-vm"/>
                <connection-factory name="RemoteConnectionFactory" entries="java:jboss/exported/jms/RemoteConnectionFactory" connectors="http-connector"/>
                <pooled-connection-factory name="activemq-ra" entries="java:/JmsXA java:jboss/DefaultJMSConnectionFactory" connectors="in-vm" transaction="xa"/>
            </server>
        </subsystem>
        <subsystem xmlns="urn:wildfly:microprofile-config-smallrye:1.0"/>
        <subsystem xmlns="urn:wildfly:microprofile-health-smallrye:2.0" security-enabled="false" empty-liveness-checks-status="${env.MP_HEALTH_EMPTY_LIVENESS_CHECKS_STATUS:UP}" empty-readiness-checks-status="${env.MP_HEALTH_EMPTY_READINESS_CHECKS_STATUS:UP}"/>
        <subsystem xmlns="urn:wildfly:microprofile-metrics-smallrye:2.0" security-enabled="false" exposed-subsystems="*" prefix="${wildfly.metrics.prefix:wildfly}"/>
        <subsystem xmlns="urn:wildfly:microprofile-opentracing-smallrye:3.0" default-tracer="jaeger">
            <jaeger-tracer name="jaeger">
                <sampler-configuration sampler-type="const" sampler-param="1.0"/>
            </jaeger-tracer>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:naming:2.0">
            <remote-naming/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:picketlink-federation:2.0">
            <federation name="federation-simple-redirect-binding">
                <identity-provider name="idp-redirect.war" url="http://${jboss.bind.address:127.0.0.1}:8080/idp-redirect/" security-domain="idp" support-signatures="false" strict-post-binding="false">
                    <trust>
                        <trust-domain name="${jboss.bind.address:127.0.0.1}"/>
                    </trust>
                </identity-provider>
                <service-providers>
                    <service-provider name="sp-redirect1.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-redirect1/" post-binding="false" support-signatures="false"/>
                    <service-provider name="sp-redirect2.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-redirect2/" post-binding="false" support-signatures="false"/>
                </service-providers>
            </federation>
            <federation name="federation-redirect-with-signatures">
                <key-store file="/jbid_test_keystore.jks" password="store123" sign-key-alias="servercert" sign-key-password="test123">
                    <keys>
                        <key name="servercert" host="${jboss.bind.address:localhost},127.0.0.1"/>
                    </keys>
                </key-store>
                <identity-provider name="idp-redirect-sig.war" url="http://${jboss.bind.address:127.0.0.1}:8080/idp-redirect-sig/" security-domain="idp" support-signatures="true" strict-post-binding="false">
                    <trust>
                        <trust-domain name="${jboss.bind.address:127.0.0.1}"/>
                    </trust>
                </identity-provider>
                <service-providers>
                    <service-provider name="sp-redirect-sig1.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-redirect-sig1/" post-binding="false" support-signatures="true"/>
                    <service-provider name="sp-redirect-sig2.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-redirect-sig2/" post-binding="false" support-signatures="true"/>
                </service-providers>
            </federation>
            <federation name="federation-simple-post-binding">
                <identity-provider name="idp-post.war" url="http://${jboss.bind.address:127.0.0.1}:8080/idp-post/" security-domain="idp">
                    <trust>
                        <trust-domain name="${jboss.bind.address:127.0.0.1}"/>
                    </trust>
                </identity-provider>
                <service-providers>
                    <service-provider name="sp-post1.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-post1/"/>
                    <service-provider name="sp-post2.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-post2/"/>
                </service-providers>
            </federation>
            <federation name="federation-post-with-signatures">
                <key-store file="/jbid_test_keystore.jks" password="store123" sign-key-alias="servercert" sign-key-password="test123">
                    <keys>
                        <key name="servercert" host="${jboss.bind.address:localhost},127.0.0.1"/>
                    </keys>
                </key-store>
                <identity-provider name="idp-post-sig.war" url="http://${jboss.bind.address:127.0.0.1}:8080/idp-post-sig/" security-domain="idp" support-signatures="true">
                    <trust>
                        <trust-domain name="${jboss.bind.address:127.0.0.1}"/>
                    </trust>
                </identity-provider>
                <service-providers>
                    <service-provider name="sp-post-sig1.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-post-sig1/" support-signatures="true"/>
                    <service-provider name="sp-post-sig2.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-post-sig2/" support-signatures="true"/>
                </service-providers>
            </federation>
            <federation name="federation-with-metadata">
                <key-store file="/jbid_test_keystore.jks" password="store123" sign-key-alias="servercert" sign-key-password="test123">
                    <keys>
                        <key name="servercert" host="${jboss.bind.address:localhost},127.0.0.1"/>
                    </keys>
                </key-store>
                <identity-provider name="idp-metadata.war" url="http://${jboss.bind.address:127.0.0.1}:8080/idp-metadata/" security-domain="idp" support-signatures="true" support-metadata="true">
                    <trust>
                        <trust-domain name="${jboss.bind.address:127.0.0.1}"/>
                    </trust>
                </identity-provider>
                <service-providers>
                    <service-provider name="sp-metadata1.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-metadata1/" support-signatures="true" support-metadata="true"/>
                    <service-provider name="sp-metadata2.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sp-metadata2/" support-signatures="true" support-metadata="true"/>
                </service-providers>
            </federation>
            <federation name="federation-with-external-idp">
                <identity-provider name="idp-external" url="http://idp.external.com/" external="true"/>
                <service-providers>
                    <service-provider name="sales-on-external.war" security-domain="sp" url="http://${jboss.bind.address:127.0.0.1}:8080/sales-on-external/" post-binding="false" support-signatures="false"/>
                </service-providers>
            </federation>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:picketlink-identity-management:2.0">
            <partition-manager name="file.complete.partition.manager" jndi-name="picketlink/FileCompletePartitionManager">
                <identity-configuration name="file.config">
                    <file-store working-dir="pl-idm-complete" relative-to="jboss.server.data.dir" always-create-files="true" async-write="true" async-write-thread-pool="10">
                        <supported-types supports-all="true"/>
                    </file-store>
                </identity-configuration>
            </partition-manager>
            <partition-manager name="file.simple.partition.manager" jndi-name="picketlink/FileSimplePartitionManager">
                <identity-configuration name="file.config">
                    <file-store>
                        <supported-types supports-all="true"/>
                    </file-store>
                </identity-configuration>
            </partition-manager>
            <partition-manager name="jpa.ds.based.partition.manager" jndi-name="picketlink/JPADSBasedPartitionManager">
                <identity-configuration name="jpa.config">
                    <jpa-store data-source="jboss/datasources/ExampleDS">
                        <supported-types supports-all="true"/>
                    </jpa-store>
                </identity-configuration>
            </partition-manager>
            <partition-manager name="jpa.emf.based.partition.manager" jndi-name="picketlink/JPAEMFBasedPartitionManager">
                <identity-configuration name="jpa.config">
                    <jpa-store entity-manager-factory="jboss/TestingIDMEMF">
                        <supported-types>
                            <supported-type name="Partition" code="Partition"/>
                            <supported-type name="IdentityType" code="IdentityType"/>
                            <supported-type name="Relationship" code="Relationship"/>
                        </supported-types>
                    </jpa-store>
                </identity-configuration>
            </partition-manager>
            <partition-manager name="jpa.emf.modules.partition.manager" jndi-name="picketlink/JPAEMFModulePartitionManager">
                <identity-configuration name="jpa.config">
                    <jpa-store entity-module="my.module" entity-module-unit-name="my-persistence-unit-name">
                        <supported-types supports-all="true"/>
                    </jpa-store>
                </identity-configuration>
            </partition-manager>
            <partition-manager name="ldap.idm" jndi-name="picketlink/LDAPBasedPartitionManager">
                <identity-configuration name="ldap.store">
                    <ldap-store url="ldap://${jboss.bind.address:127.0.0.1}:10389" bind-dn="uid=admin,ou=system" bind-credential="secret" base-dn-suffix="dc=jboss,dc=org">
                        <supported-types supports-all="false">
                            <supported-type name="IdentityType" code="IdentityType"/>
                            <supported-type name="Relationship" code="Relationship"/>
                        </supported-types>
                        <mappings>
                            <mapping name="Agent" code="Agent" base-dn-suffix="ou=Agent,dc=jboss,dc=org" object-classes="account">
                                <attribute name="loginName" ldap-name="uid" is-identifier="true"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                            <mapping name="User" code="User" base-dn-suffix="ou=People,dc=jboss,dc=org" object-classes="inetOrgPerson, organizationalPerson">
                                <attribute name="loginName" ldap-name="uid" is-identifier="true"/>
                                <attribute name="firstName" ldap-name="cn"/>
                                <attribute name="lastName" ldap-name="sn"/>
                                <attribute name="email" ldap-name="mail"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                            <mapping name="Role" code="Role" base-dn-suffix="ou=Roles,dc=jboss,dc=org" object-classes="groupOfNames">
                                <attribute name="name" ldap-name="cn" is-identifier="true"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                            <mapping name="Group" code="Group" base-dn-suffix="ou=Groups,dc=jboss,dc=org" object-classes="groupOfNames">
                                <attribute name="name" ldap-name="cn" is-identifier="true"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                            <mapping name="Grant" code="Grant" relates-to="Role">
                                <attribute name="assignee" ldap-name="member"/>
                            </mapping>
                        </mappings>
                    </ldap-store>
                </identity-configuration>
            </partition-manager>
            <partition-manager name="multiple.store.idm" jndi-name="picketlink/MultipleStoreBasedPartitionManager">
                <identity-configuration name="multiple.store">
                    <jpa-store data-source="jboss/datasources/ExampleDS" support-credential="false" support-attribute="false">
                        <supported-types supports-all="false">
                            <supported-type name="Relationship" class-name="org.picketlink.idm.model.Relationship"/>
                        </supported-types>
                    </jpa-store>
                    <ldap-store url="ldap://${jboss.bind.address:127.0.0.1}:10389" bind-dn="uid=admin,ou=system" bind-credential="secret" base-dn-suffix="dc=jboss,dc=org" support-attribute="false" support-credential="true">
                        <supported-types supports-all="false">
                            <supported-type name="IdentityType" code="IdentityType"/>
                        </supported-types>
                        <mappings>
                            <mapping name="Agent" code="Agent" base-dn-suffix="ou=Agent,dc=jboss,dc=org" object-classes="account">
                                <attribute name="loginName" ldap-name="uid" is-identifier="true"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                            <mapping name="User" code="User" base-dn-suffix="ou=People,dc=jboss,dc=org" object-classes="inetOrgPerson, organizationalPerson">
                                <attribute name="loginName" ldap-name="uid" is-identifier="true"/>
                                <attribute name="firstName" ldap-name="cn" read-only="false"/>
                                <attribute name="lastName" ldap-name="sn" read-only="false"/>
                                <attribute name="email" ldap-name="mail" read-only="false"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                            <mapping name="Role" code="Role" base-dn-suffix="ou=Roles,dc=jboss,dc=org" object-classes="groupOfNames">
                                <attribute name="name" ldap-name="cn" is-identifier="true"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                            <mapping name="Group" code="Group" base-dn-suffix="ou=Groups,dc=jboss,dc=org" object-classes="groupOfNames">
                                <attribute name="name" ldap-name="cn" is-identifier="true"/>
                                <attribute name="createdDate" ldap-name="createTimeStamp" read-only="true"/>
                            </mapping>
                        </mappings>
                    </ldap-store>
                </identity-configuration>
            </partition-manager>
            <partition-manager name="multiple.config.partition.manager" jndi-name="picketlink/MultipleConfigurationPartitionManager">
                <identity-configuration name="partition.jboss.config">
                    <jpa-store data-source="jboss/datasources/JBossIdentityDS">
                        <supported-types supports-all="true"/>
                    </jpa-store>
                </identity-configuration>
                <identity-configuration name="partition.redhat.config">
                    <jpa-store data-source="jboss/datasources/RedHatIdentityDS">
                        <supported-types supports-all="true"/>
                    </jpa-store>
                </identity-configuration>
            </partition-manager>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:pojo:1.0"/>
        <subsystem xmlns="urn:jboss:domain:remoting:4.0">
            <http-connector name="http-remoting-connector" connector-ref="default" security-realm="ApplicationRealm"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:request-controller:1.0"/>
        <subsystem xmlns="urn:jboss:domain:resource-adapters:6.0"/>
        <subsystem xmlns="urn:jboss:domain:sar:1.0"/>
        <subsystem xmlns="urn:jboss:domain:security:2.0">
            <security-domains>
                <security-domain name="other" cache-type="default">
                    <authentication>
                        <login-module code="Remoting" flag="optional">
                            <module-option name="password-stacking" value="useFirstPass"/>
                        </login-module>
                        <login-module code="RealmDirect" flag="required">
                            <module-option name="password-stacking" value="useFirstPass"/>
                        </login-module>
                    </authentication>
                </security-domain>
                <security-domain name="idp" cache-type="default">
                    <authentication>
                        <login-module code="UsersRoles" flag="required">
                            <module-option name="usersProperties" value="users.properties"/>
                            <module-option name="rolesProperties" value="roles.properties"/>
                        </login-module>
                    </authentication>
                </security-domain>
                <security-domain name="sp" cache-type="default">
                    <authentication>
                        <login-module code="org.picketlink.identity.federation.bindings.jboss.auth.SAML2LoginModule" flag="required"/>
                    </authentication>
                </security-domain>
            </security-domains>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:security-manager:1.0">
            <deployment-permissions>
                <maximum-set>
                    <permission class="java.security.AllPermission"/>
                </maximum-set>
            </deployment-permissions>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:transactions:5.0">
            <core-environment node-identifier="${jboss.tx.node.id:1}">
                <process-id>
                    <uuid/>
                </process-id>
            </core-environment>
            <recovery-environment socket-binding="txn-recovery-environment" status-socket-binding="txn-status-manager"/>
            <coordinator-environment statistics-enabled="${wildfly.transactions.statistics-enabled:${wildfly.statistics-enabled:false}}"/>
            <object-store path="tx-object-store" relative-to="jboss.server.data.dir"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:undertow:11.0" default-server="default-server" default-virtual-host="default-host" default-servlet-container="default" default-security-domain="other" statistics-enabled="${wildfly.undertow.statistics-enabled:${wildfly.statistics-enabled:false}}">
            <buffer-cache name="default"/>
            <server name="default-server">
                <http-listener name="default" socket-binding="http" redirect-socket="https" enable-http2="true"/>
                <https-listener name="https" socket-binding="https" security-realm="ApplicationRealm" enable-http2="true"/>
                <host name="default-host" alias="localhost">
                    <location name="/" handler="welcome-content"/>
                    <http-invoker security-realm="ApplicationRealm"/>
                </host>
            </server>
            <servlet-container name="default">
                <jsp-config/>
                <websockets/>
            </servlet-container>
            <handlers>
                <file name="welcome-content" path="${jboss.home.dir}/welcome-content"/>
            </handlers>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:webservices:2.0" statistics-enabled="${wildfly.webservices.statistics-enabled:${wildfly.statistics-enabled:false}}">
            <wsdl-host>${jboss.bind.address:127.0.0.1}</wsdl-host>
            <endpoint-config name="Standard-Endpoint-Config"/>
            <endpoint-config name="Recording-Endpoint-Config">
                <pre-handler-chain name="recording-handlers" protocol-bindings="##SOAP11_HTTP ##SOAP11_HTTP_MTOM ##SOAP12_HTTP ##SOAP12_HTTP_MTOM">
                    <handler name="RecordingHandler" class="org.jboss.ws.common.invocation.RecordingServerHandler"/>
                </pre-handler-chain>
            </endpoint-config>
            <client-config name="Standard-Client-Config"/>
        </subsystem>
        <subsystem xmlns="urn:jboss:domain:weld:4.0"/>
    </profile>
    <interfaces>
        <interface name="management">
            <inet-address value="${jboss.bind.address.management:127.0.0.1}"/>
        </interface>
        <interface name="public">
            <inet-address value="${jboss.bind.address:127.0.0.1}"/>
        </interface>
        <interface name="unsecure">
            <inet-address value="${jboss.bind.address.unsecure:127.0.0.1}"/>
        </interface>
    </interfaces>
    <socket-binding-group name="standard-sockets" default-interface="public" port-offset="${jboss.socket.binding.port-offset:0}">
        <socket-binding name="ajp" port="${jboss.ajp.port:8009}"/>
        <socket-binding name="http" port="${jboss.http.port:8080}"/>
        <socket-binding name="https" port="${jboss.https.port:8443}"/>
        <socket-binding name="iiop" interface="unsecure" port="3528"/>
        <socket-binding name="iiop-ssl" interface="unsecure" port="3529"/>
        <socket-binding name="management-http" interface="management" port="${jboss.management.http.port:9990}"/>
        <socket-binding name="management-https" interface="management" port="${jboss.management.https.port:9993}"/>
        <socket-binding name="txn-recovery-environment" port="4712"/>
        <socket-binding name="txn-status-manager" port="4713"/>
        <outbound-socket-binding name="mail-smtp">
            <remote-destination host="${jboss.mail.server.host:localhost}" port="${jboss.mail.server.port:25}"/>
        </outbound-socket-binding>
    </socket-binding-group>
</server>