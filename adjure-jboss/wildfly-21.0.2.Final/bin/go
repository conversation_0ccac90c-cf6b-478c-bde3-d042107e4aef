#!/bin/bash

source /h/adjure/base/config/adjure_env.sh

if [ -z "$JAVA_HOME" ]
then
  export JAVA_HOME=/usr/java/default
fi

#
# Add configured "extra" native library paths
#
if [ -n "$ADJURE_WILDFLY_EXTRA_NATIVE_LIBS_DIRS" ]; then
  if [ -z "$LD_LIBRARY_PATH" ]; then
    export LD_LIBRARY_PATH=$ADJURE_WILDFLY_EXTRA_NATIVE_LIBS_DIRS
  else
    export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:$ADJURE_WILDFLY_EXTRA_NATIVE_LIBS_DIRS
  fi
fi

#
# Set JBOSS_HOME if not already set
#
if [ -z "$JBOSS_HOME" ]
then
  cd ..
  export JBOSS_HOME=`pwd`
  cd -
fi

#
# Add $JBOSS_HOME/adjure-modules to the module path
#
export JBOSS_MODULEPATH=$JBOSS_HOME/modules:$JBOSS_HOME/adjure-modules

#
# Make all the common adjure scripts available
#
. $ADJURE_SCRIPTING_COMMON
#
# Make all the wildfly adjure tools available
#
. $ADJURE_WILDFLY_TOOLS

#
# Run all the extension scripts
#
if [ -e "ext" ];
then
  for f in $(find ext -maxdepth 1 -type f); do
    #echo "Running JBOSS startup extension script: " $f
    . $f
  done
fi

#
# If not already set from an extension startup script, set it here
# This ensures that the standalone script will have a set value for RUN_CONF
#
if [ -z "$RUN_CONF" ]
then
  export RUN_CONF=$JBOSS_HOME/bin/standalone-adjure.conf
fi

#
# Get all the deployment-defined system properties as a starting point
#
system_props=$(get_adjure_base_config ADJURE_WILDFLY_SYSTEM_PROPS '')

#
# set_system_prop <property to set> <value to use>
#
function set_system_prop {

    if [[ -z "$system_props" ]]; then
        if [[ "$2" != "" ]]; then
            system_props="-D$1=$2"
        fi
    else
        local exists=$(echo "$system_props" | sed "s/^.*-D$1=.*$/found/")
        if [[ "$exists" != "found" ]]; then
            if [[ "$2" != "" ]]; then
                system_props="$system_props -D$1=$2"
            fi
        else
            if [[ "$2" != "" ]]; then
                system_props=$(replace_property "$system_props" $1 "$2")
            else
                system_props=$(replace_property "$system_props" $1 "")
            fi
        fi
    fi

}

#
# Set the JICD 4.2 properties override file
#
if [ -n "$WILDFLY_J42_OVERRIDE_FILE" ]; then
  set_system_prop jicd.properties.file "$WILDFLY_J42_OVERRIDE_FILE"
fi

#
# Set the HTTP and HTTPS ports
#
if [[ -n "$WILDFLY_HTTP_PORT" ]]; then
    #
    # Set the http port
    #
    set_system_prop jboss.http.port $WILDFLY_HTTP_PORT
fi

if [[ -n "$WILDFLY_HTTPS_PORT" ]]; then
    #
    # Set the https port
    #
    set_system_prop jboss.https.port $WILDFLY_HTTPS_PORT
fi

logfile_dir="$ADJURE_LOG_ROOT/wildfly"

#
# Set EJB max thread count
#
set_system_prop adjure_ejb_max_thread_count "$ADJURE_WILDFLY_EJB_THREAD_COUNT"

#
# Set server-wide locations for keystore and truststore:
# Note: this must agree with settings in the installed standalone-adjure.xml
#
set_system_prop javax.net.ssl.keyStore "$ADJURE_WILDFLY_DATADIR/configuration/adjure-keystore.jks"
set_system_prop javax.net.ssl.keyStorePassword secret
set_system_prop javax.net.ssl.trustStore "$ADJURE_WILDFLY_DATADIR/configuration/adjure-truststore.jks"
set_system_prop javax.net.ssl.trustStorePassword secret

#
# Set the base directory. The base directory is the directory under which
# deployments, data, etc. exist, unless specified elsewhere for each
# - different for sce vs linux, and the ADJURE_WILDFLY_DATADIR has the correct value
#
set_system_prop jboss.server.base.dir "$ADJURE_WILDFLY_DATADIR"

#
# Set the temporary directory for jna
#
set_system_prop jna.tmpdir "$ADJURE_WILDFLY_BASE/jna_tmpdir"

#
# Set the log root directory
#
set_system_prop jboss.server.log.dir "$logfile_dir"

#
# Remote access causes a memory leak that is best fixed with the following system property.
# This will use regular GC to manage buffers at a slight performance cost.
#
set_system_prop jboss.remoting.pooled-buffers false

#
# Always configure the default timezone to UTC
#
set_system_prop user.timezone UTC
#
# Warn when resolving remote XML DTDs or schemas.
#
set_system_prop java.net.preferIPv4Stack true
set_system_prop org.jboss.resolver.warning true

#
# Set both client and server RMI gc interval
#
set_system_prop sun.rmi.dgc.client.gcInterval 3600000
set_system_prop sun.rmi.dgc.server.gcInterval 3600000

#
# Append all deployment-specified system properties (and the properties we defined above) to JAVA_OPTS, not PREPEND_JAVA_OPTS
# (PREPEND_JAVA_OPTS is not examined for system properties when determining directory overrides)
#
export JAVA_OPTS="$JAVA_OPTS $system_props"

#
# Adjust min/max memory settings
#
# For now, do not set MaxMetaspaceSize.
# For reference, the default standalone.sh script set MaxMetaspaceSize to 256m
#
export PREPEND_JAVA_OPTS="$PREPEND_JAVA_OPTS -Xms128m -Xmx${ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG}m -XX:MetaspaceSize=96m"

#
# Remove the history of changes to standalone configuration before starting wildfly
#
rm -rf ${ADJURE_WILDFLY_DATADIR}/configuration/standalone_xml_history

#
# The standalone-adjure.xml file is tied to adjure, but not any specific deployment.
#
# There is the possibility that the previous execution of wildfly was killed, preventing any non-auto-deployed apps from being undeployed.
# When this happens, the standalone XML file still contains those deployments, and running wildfly will attempt to start those deployments.
# This causes inconsistent wildfly startup behavior, which we want to avoid.
# To prevent this, we copy standalone-adjure.xml to standalone-adjure-running.xml and start wildfly with standalone-adjure-running.xml.
#   This prevents the problem because standalone-adjure.xml does NOT include these deployments, so wildfly is started from the same XML file each time.
# As part of copying this file, we also run envsubst to replace any references to environment variable references
#   with the value for that environment variable before starting wildfly.
# We don't need to set the ownership or access to the "running" XML file, since only wildfly needs to (or should) change it.
#   Since we are running wildfly, and we created the file, wildfly can access the file.
#   Also, the file is created as writeable by owner, and readable by all other users.
#
running_xml_file_name="standalone-adjure-running.xml"
running_file="${ADJURE_WILDFLY_DATADIR}/configuration/$running_xml_file_name"
orig_file="${ADJURE_WILDFLY_DATADIR}/configuration/standalone-adjure.xml"
rm -f "${running_file}"
cat "${orig_file}" | DOLLAR='$' envsubst > ${running_file}

script_args_opt=""
mkdir -p "$logfile_dir"
if [ "$ADJURE_WILDFLY_SECMGR" = "true" ]; then
    script_args_opt="$script_args_opt -secmgr";
fi
./standalone-adjure.sh ${script_args_opt} -c $running_xml_file_name 2>&1 | ./rotatelogs.py "$logfile_dir/console.log" 10M &
