#
# J<PERSON><PERSON>, Home of Professional Open Source.
# Copyright 2010, Red Hat, Inc., and individual contributors
# as indicated by the <AUTHOR> See the copyright.txt file in the
# distribution for a full listing of individual contributors.
#
# This is free software; you can redistribute it and/or modify it
# under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of
# the License, or (at your option) any later version.
#
# This software is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this software; if not, write to the Free
# Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
# 02110-1301 USA, or see the FSF site: http://www.fsf.org.
#

#
# Password restriction
#

# Valid values: RELAX, WARN or REJECT
# RELAX : Don't perform any strength checks on the password in both interactive and non-interactive mode
# WARN : Display a message about the strength of the password. Ask confirmation if the password is weak in interactive mode
# REJECT : Display a message about the strength of the password (if the password is weak, the user is not created).
# Ask confirmation if the password is weak in interactive mode
password.restriction=WARN

# Password minimum length
password.restriction.minLength=8

# Password must contains at least one alpha
password.restriction.minAlpha=1

# Password must contains at least one digit
password.restriction.minDigit=1

# Password must contains at least one symbol
password.restriction.minSymbol=1

# Password must not match the username. Valid values: TRUE or FALSE.
password.restriction.mustNotMatchUsername=TRUE

# Comma separated list of forbidden passwords (easily guessable)
password.restriction.forbiddenValue=root,admin,administrator

# Password strength. Valid values: VERY_WEAK, WEAK, MODERATE, MEDIUM, STRONG, VERY_STRONG or EXCEPTIONAL.
# If not present, it defaults to "MODERATE"
password.restriction.strength=MEDIUM

# Class of password strength checker.
# If not present, utility will revert to default implementation
password.restriction.checker=org.jboss.as.domain.management.security.password.simple.SimplePasswordStrengthChecker