#
# J<PERSON><PERSON>, Home of Professional Open Source.
# Copyright 2010, Red Hat, Inc., and individual contributors
# as indicated by the <AUTHOR> See the copyright.txt file in the
# distribution for a full listing of individual contributors.
#
# This is free software; you can redistribute it and/or modify it
# under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of
# the License, or (at your option) any later version.
#
# This software is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this software; if not, write to the Free
# Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
# 02110-1301 USA, or see the FSF site: http://www.fsf.org.
#

# Additional logger names to configure (root logger is always configured)
loggers=org,javax,org.jboss.as.cli,org.aesh
logger.org.level=OFF
logger.javax.level=OFF
# assign a lower level to enable CLI logging
logger.org.jboss.as.cli.level=OFF
# assign a lower level to enable aesh logging
logger.org.aesh.level=OFF

# Root logger level
logger.level=${jboss.cli.log.level:INFO}
# Root logger handlers
# uncomment to enable logging to the file
#logger.handlers=FILE

# File handler configuration
handler.FILE=org.jboss.logmanager.handlers.FileHandler
handler.FILE.level=DEBUG
handler.FILE.properties=autoFlush,fileName
handler.FILE.autoFlush=true
handler.FILE.fileName=${jboss.cli.log.file:jboss-cli.log}
handler.FILE.formatter=PATTERN

# Formatter pattern configuration
formatter.PATTERN=org.jboss.logmanager.formatters.PatternFormatter
formatter.PATTERN.properties=pattern
formatter.PATTERN.pattern=%d{HH:mm:ss,SSS} %-5p [%c] %s%e%n
