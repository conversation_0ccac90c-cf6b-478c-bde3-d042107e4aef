log4j.rootLogger=warn, stdout, R

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout

# Pattern to output the caller's file name and line number.
log4j.appender.stdout.layout.ConversionPattern=%5p [%X{key}] (%F:%L) - %m%n

log4j.appender.R=org.apache.log4j.RollingFileAppender
log4j.appender.R.File=/h/jboss/wildfly-9.0.0.Final/standalone/log/netscore.log

log4j.appender.R.MaxFileSize=60MB
# Keep one backup file
log4j.appender.R.MaxBackupIndex=1

log4j.appender.R.layout=org.apache.log4j.PatternLayout
log4j.appender.R.layout.ConversionPattern=%d{yyMMdd}T%d{HHmmss.SSS}z,[%t],%c{1},%p,%F,%L: %m%n
