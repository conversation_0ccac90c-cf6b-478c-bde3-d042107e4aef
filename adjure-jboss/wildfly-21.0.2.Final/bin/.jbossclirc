# .jbossclirc
#
# WildFly (JBoss) CLI will execute commands from this file before
# the command line session (interactive or not) will start.
#
# If script argument --properties was used when launching the CLI,
# the commands from this file will be executed after the system
# properties have been set.
#
# This file would typically be used to setup the CLI session
# variables (which is optional at the moment), e.g.
#
# set myvar=/subsystem=datasources/data-source=ExampleDS
