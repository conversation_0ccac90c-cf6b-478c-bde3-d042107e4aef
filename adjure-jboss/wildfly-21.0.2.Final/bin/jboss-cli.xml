<?xml version='1.0' encoding='UTF-8'?>

<!--
   WildFly Command-line Interface configuration.
-->
<jboss-cli xmlns="urn:jboss:cli:3.4">

    <default-protocol use-legacy-override="true">remote+http</default-protocol>

    <!-- The default controller to connect to when 'connect' command is executed w/o arguments -->
    <default-controller>
        <protocol>remote+http</protocol>
        <host>localhost</host>
        <port>9990</port>
    </default-controller>

    <!-- Example controller alias named 'Test'
    <controllers>
        <controller name="Test">
            <protocol>remote+http</protocol>
            <host>localhost</host>
            <port>9990</port>
        </controller>
    </controllers>
    -->

    <validate-operation-requests>true</validate-operation-requests>

    <!-- Command and operation history log configuration -->
    <history>
        <enabled>true</enabled>
        <file-name>.jboss-cli-history</file-name>
        <file-dir>${user.home}</file-dir>
        <max-size>500</max-size>
    </history>

    <!-- whether to resolve system properties specified as command argument or operation parameter values
                  in the CLI VM before sending the operation requests to the controller -->
    <resolve-parameter-values>false</resolve-parameter-values>


    <!-- Whether to write info and error messages to the terminal output -->
    <silent>false</silent>

    <!-- Whether to filter out commands and attributes based on user's permissions -->
    <access-control>false</access-control>

    <!-- Include the prompt with the command into the output for each command executed in non-interactive mode -->
    <echo-command>false</echo-command>

    <!-- Uncomment to set the command timeout. Element value is in seconds -->
    <!-- <command-timeout>30</command-timeout> -->

    <!-- Uncomment to display operation responses using JSON syntax.
    By default responses are displayed using DMR string syntax. -->
    <!-- <output-json>true</output-json> -->

    <!-- Configuration of CLI colors. To disable, change <enabled> to false.
    Available colors: black, blue, cyan, green, magenta, red, white, yellow and default, which is the terminal's default
    foreground color-->
    <color-output>
        <enabled>true</enabled>
        <error-color>red</error-color>
        <warn-color>yellow</warn-color>
        <success-color>default</success-color>
        <required-color>magenta</required-color>
        <workflow-color>green</workflow-color>
        <prompt-color>blue</prompt-color>
    </color-output>

    <!-- Uncomment to disable the output paging.
    By default, if the OS supports it, the output of commands is not printed at once, instead you can scroll through it and search
    in it. This behaviour is similar to 'less' linux command.
    When this option is set to false the paging will be disabled
    and the whole output of all commands will be written at once(useful e.g.for copy/paste).-->
    <!-- <output-paging>false</output-paging> -->

</jboss-cli>
