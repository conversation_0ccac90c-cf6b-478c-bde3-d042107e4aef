#!/usr/bin/env bash

. /h/adjure/base/config/adjure_env.sh
. $ADJURE_SCRIPTING_COMMON
. $ADJURE_WILDFLY_TOOLS
. $ADJURE_WILDFLY_RECONFIGURE_TOOLS

#
# These two values (shiro, dashboard-authorization) must match
# the associated values in the shiro ini file for the server, and
# in the web.xml file for the server
#
add_infinispan_web_cache shiro dashboard-authorization
auth_retval=$?
# > 1 means a failure
if [ $auth_retval -gt 1 ]; then
    return $auth_retval
fi
# auth-cache is configured; auth_retval=1 if no change, 0 if change
#
# now configure sessions-cache
#
# These two values (shiro, dashboard-authorization) must match
# the associated values in the shiro ini file for the server, and
# in the web.xml file for the server
#
add_infinispan_web_cache shiro dashboard-active-sessions
sessions_retval=$?
# > 1 means a failure
if [ $sessions_retval -gt 1 ]; then
    return $sessions_retval
fi
# both are now configured
# if either changed the configuration, return that the configuration was changed (0)
if [ $sessions_retval -eq 0 ]; then
    return 0
fi
if [ $auth_retval -eq 0 ]; then
    return 0
fi
# return that the configuration was unchanged (1)
return 1
