#!/usr/bin/python

from __future__ import print_function

import sys

filename = ''
max_bytes = 0

def handle_args(argv):
    if len(argv) < 3:
        print("Usage: {0} filename max_size".format(argv[0]))
        sys.exit(1)
    global filename, max_bytes
    filename = argv[1]
    max_bytes = calculate_bytes(argv[2])

def calculate_bytes(maxsize):
    if len(maxsize) == 0:
        return 0
    if maxsize[-1] == 'k' or maxsize[-1] == 'K':
        return int(maxsize[:-1]) * 1024
    if maxsize[-1] == 'm' or maxsize[-1] == 'M':
        return int(maxsize[:-1]) * 1024 * 1024
    if maxsize[-1] == 'g' or maxsize[-1] == 'G':
        return int(maxsize[:-1]) * 1024 * 1024 * 1024
    return int(maxsize)

if __name__ == '__main__':
    handle_args(sys.argv)
    # print("Logging to {0} max-bytes:{1}".format(filename, max_bytes))
    output = open(filename, 'w')
    line = sys.stdin.readline()
    bytes_written = len(line)
    while line:
        output.write(line)
        output.flush()
        if bytes_written > max_bytes:
            bytes_written = 0
            output.close
            output = open(filename, 'w')
        line = sys.stdin.readline()
        bytes_written += len(line)
    output.close()








