#
# J<PERSON><PERSON>, Home of Professional Open Source.
# Copyright 2010, Red Hat, Inc., and individual contributors
# as indicated by the <AUTHOR> See the copyright.txt file in the
# distribution for a full listing of individual contributors.
#
# This is free software; you can redistribute it and/or modify it
# under the terms of the GNU Lesser General Public License as
# published by the Free Software Foundation; either version 2.1 of
# the License, or (at your option) any later version.
#
# This software is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this software; if not, write to the Free
# Software Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
# 02110-1301 USA, or see the FSF site: http://www.fsf.org.
#

# Additional loggers to configure (the root logger is always configured)
loggers=jacorb,com.arjuna,jacorb.config,sun.rmi

logger.level=INFO
logger.handlers=CONSOLE,FILE

logger.com.arjuna.level=WARN
logger.com.arjuna.useParentHandlers=true

logger.sun.rmi.level=WARN
logger.sun.rmi.useParentHandlers=true

handler.CONSOLE=org.jboss.logmanager.handlers.ConsoleHandler
handler.CONSOLE.level=INFO
handler.CONSOLE.formatter=CONSOLE
handler.CONSOLE.properties=autoFlush,target
handler.CONSOLE.autoFlush=true
handler.CONSOLE.target=SYSTEM_OUT

handler.FILE=org.jboss.logmanager.handlers.PeriodicRotatingFileHandler
handler.FILE.level=INFO
handler.FILE.formatter=FILE
handler.FILE.properties=autoFlush,append,fileName,suffix
handler.FILE.constructorProperties=fileName,append
handler.FILE.autoFlush=true
handler.FILE.append=true
handler.FILE.fileName=${org.jboss.boot.log.file:boot.log}
handler.FILE.suffix=.yyyy-MM-dd

formatter.CONSOLE=org.jboss.logmanager.formatters.PatternFormatter
formatter.CONSOLE.properties=pattern
formatter.CONSOLE.pattern=%K{level}%d{HH\:mm\:ss,SSS} %-5p [%c] (%t) %s%E%n

formatter.FILE=org.jboss.logmanager.formatters.PatternFormatter
formatter.FILE.properties=pattern
formatter.FILE.constructorProperties=pattern
formatter.FILE.pattern=%d{yyyy-MM-dd HH\:mm\:ss,SSS} %-5p [%c] (%t) %s%E%n
