<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ticomgeo.adjure.platform</groupId>
        <artifactId>wildfly-deployment-session-parent</artifactId>
        <version>17.1.0</version>
    </parent>

    <artifactId>wildfly-deployment-session-war</artifactId>
    <packaging>war</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>analyze-deps</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>analyze-only</goal>
                        </goals>
                        <configuration>
                            <usedDependencies>
                                <usedDependency>com.google.protobuf:protobuf-java</usedDependency>

                                <!-- add logging dependencies -->
                                <usedDependency>commons-logging:commons-logging</usedDependency>
                                <usedDependency>org.slf4j:slf4j-api</usedDependency>
                                <usedDependency>org.apache.logging.log4j:log4j-1.2-api</usedDependency>
                                <usedDependency>org.apache.logging.log4j:log4j-slf4j-impl</usedDependency>
                                <usedDependency>org.apache.logging.log4j:log4j-jcl</usedDependency>
                                <usedDependency>org.apache.logging.log4j:log4j-core</usedDependency>
                                <usedDependency>org.apache.logging.log4j:log4j-web</usedDependency>
                                <usedDependency>log4j:log4j</usedDependency>

                                <usedDependency>${groupId.java-common}:java-util</usedDependency>
                                <usedDependency>${groupId.ic-common}:impl-common</usedDependency>

                                <!-- force pulling in of Settings interfaces/classes from Adjure, jee-framework, and java-common -->
                                <usedDependency>${groupId.java-common}:java-common-cfg-impl</usedDependency>
                                <usedDependency>${groupId.jee-framework}:jee-framework-cfg-impl</usedDependency>
                                <usedDependency>${groupId.adjure.core}:adjure-cfg-impl</usedDependency>

                                <!-- binding of service client & provider messaging to JMS -->
                                <usedDependency>${groupId.jee-framework}:service-messaging-jms</usedDependency>
                                <usedDependency>${groupId.jee-framework}:jee-messaging-jms</usedDependency>
                                <usedDependency>${groupId.jee-framework}:jms-messaging-qpid</usedDependency>
                                <!-- binding of transaction model, async, timer, startup interface to JEE (EJB) -->
                                <usedDependency>${groupId.jee-framework}:ejb-bean-binding</usedDependency>

                                <!-- binding to jboss container -->
                                <usedDependency>${project.groupId}:wildfly-deployment-binding-common</usedDependency>

                                <!-- force use of proto generated code compiled with 2.4.1 -->
                                <usedDependency>${groupId.adjure.core}:adjure-gpb-${version.protobuf.tools}</usedDependency>

                                <!-- session service provider -->
                                <usedDependency>${groupId.adjure.core}:session-service-provider</usedDependency>

                                <!-- application binding -->
                                <usedDependency>${project.groupId}:wildfly-deployment-session-war-binding</usedDependency>
                            </usedDependencies>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.wildfly.plugins</groupId>
                <artifactId>wildfly-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-1.2-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-jcl</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>${groupId.java-common}</groupId>
            <artifactId>java-util</artifactId>
        </dependency>
        <!-- force inclusion of desired jars -->
        <dependency>
            <groupId>${groupId.ic-common}</groupId>
            <artifactId>impl-common</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.java-common}</groupId>
            <artifactId>java-common-cfg-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>jee-messaging-jms</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>jms-messaging-qpid</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>ejb-bean-binding</artifactId>
        </dependency>
        <!-- Source implementation of Service API modules -->
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>adjure-gpb-${version.protobuf.tools}</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>session-service-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>adjure-cfg-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>service-messaging-jms</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>jee-framework-cfg-impl</artifactId>
        </dependency>
        <!-- application implementations for required interfaces -->
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>wildfly-deployment-binding-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>wildfly-deployment-session-war-binding</artifactId>
            <version>${project.version}</version>
        </dependency>
    </dependencies>
</project>
