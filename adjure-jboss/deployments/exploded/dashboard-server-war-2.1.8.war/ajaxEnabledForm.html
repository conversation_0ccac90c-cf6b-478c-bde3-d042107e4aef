<html>
<head>
<title>ajaxEnabledForm.html</title>
<script type="text/javascript">
<!--
//Browser Support Code
function ajaxPollNewEvents(){
  var ajaxRequest;  // The variable that makes Ajax possible!

  try{
    // Opera 8.0+, Firefox, Safari
    ajaxRequest = new XMLHttpRequest();
  } catch (e){
    // Internet Explorer Browsers
    try{
      ajaxRequest = new ActiveXObject("Msxml2.XMLHTTP");
    } catch (e) {
      try{
        ajaxRequest = new ActiveXObject("Microsoft.XMLHTTP");
      } catch (e){
        // Something went wrong
        alert("Your browser broke!");
        return false;
      }
    }
  }
  // Create a function that will receive data sent from the server
  ajaxRequest.onreadystatechange = function(){
    if(ajaxRequest.readyState == 4){
		  var ajaxDisplay = document.getElementById('ajaxDiv');
			ajaxDisplay.innerHTML = ajaxRequest.responseText;
    }
  }


  var sessionid = document.getElementById('sessionid').value;
  ajaxRequest.open("GET", "rest/json/fflysession/events/"+sessionid, true);
  ajaxRequest.send(null);
}

function ajaxStepRota(){
	  var ajaxRequest;  // The variable that makes Ajax possible!

	  try{
	    // Opera 8.0+, Firefox, Safari
	    ajaxRequest = new XMLHttpRequest();
	  } catch (e){
	    // Internet Explorer Browsers
	    try{
	      ajaxRequest = new ActiveXObject("Msxml2.XMLHTTP");
	    } catch (e) {
	      try{
	        ajaxRequest = new ActiveXObject("Microsoft.XMLHTTP");
	      } catch (e){
	        // Something went wrong
	        alert("Your browser broke!");
	        return false;
	      }
	    }
	  }
	  // Create a function that will receive data sent from the server
	  ajaxRequest.onreadystatechange = function(){
	    if(ajaxRequest.readyState == 4){
			  var ajaxDisplay = document.getElementById('ajaxDiv');
				ajaxDisplay.innerHTML = ajaxRequest.responseText;
	    }
	  }


	  var sessionid = document.getElementById('sessionid').value;
	  var uuid = document.getElementById('uuid').value;
	  ajaxRequest.open(
			"POST",
			"rest/json/rota/step/" + sessionid + "/" + uuid,
			true
		);
		ajaxRequest.setRequestHeader("Content-type","application/json");
	  ajaxRequest.send("{\"sessionid\":"+sessionid+",\"stepId\":\""+uuid+"\"}");
}

function ajaxDestroySession(){
	  var ajaxRequest;  // The variable that makes Ajax possible!

	  try{
	    // Opera 8.0+, Firefox, Safari
	    ajaxRequest = new XMLHttpRequest();
	  } catch (e){
	    // Internet Explorer Browsers
	    try{
	      ajaxRequest = new ActiveXObject("Msxml2.XMLHTTP");
	    } catch (e) {
	      try{
	        ajaxRequest = new ActiveXObject("Microsoft.XMLHTTP");
	      } catch (e){
	        // Something went wrong
	        alert("Your browser broke!");
	        return false;
	      }
	    }
	  }
	  // Create a function that will receive data sent from the server
	  ajaxRequest.onreadystatechange = function(){
	    if(ajaxRequest.readyState == 4){
			  var ajaxDisplay = document.getElementById('ajaxDiv');
				ajaxDisplay.innerHTML = ajaxRequest.responseText;
	    }
	  }


	  var sessionid = document.getElementById('sessionid').value;
	  ajaxRequest.open("DELETE", "rest/json/fflysession/" + sessionid, true);
	  ajaxRequest.send(null);
}

function ajaxMirrorBack(){
	  var ajaxRequest;  // The variable that makes Ajax possible!

	  try{
	    // Opera 8.0+, Firefox, Safari
	    ajaxRequest = new XMLHttpRequest();
	  } catch (e){
	    // Internet Explorer Browsers
	    try{
	      ajaxRequest = new ActiveXObject("Msxml2.XMLHTTP");
	    } catch (e) {
	      try{
	        ajaxRequest = new ActiveXObject("Microsoft.XMLHTTP");
	      } catch (e){
	        // Something went wrong
	        alert("Your browser broke!");
	        return false;
	      }
	    }
	  }
	  // Create a function that will receive data sent from the server
	  ajaxRequest.onreadystatechange = function(){
	    if(ajaxRequest.readyState == 4){
			  var ajaxDisplay = document.getElementById('ajaxDiv');
				ajaxDisplay.innerHTML = ajaxRequest.responseText;
	    }
	  }


	  ajaxRequest.open(
			"POST",
			"rest/json/mirror",
			true
		);
		ajaxRequest.setRequestHeader("Content-type","application/json");
	  ajaxRequest.send("{\"minFreqRange\":1000,\"maxFreqRange\":1000000,\"channelSpacing\":100}");

}
//-->
</script>
</head>
<body>


<form name='myForm'>
Your SessionId: <input type='text' id='sessionid' />
<br/>
UUID String: <input type='text' id='uuid' value='123-234-234-234-234' />
<br/>

<input type='button' onclick='ajaxPollNewEvents()' value='Poll New Events' />
<br/>
<input type='button' onclick='ajaxDestroySession()' value='Destroy Session' />
<br/>
<input type='button' onclick='ajaxStepRota()' value='Step Rota' />
<br/>
<input type='button' onclick='ajaxMirrorBack()' value='Step Rota' />
<br/>
</form>

<div id='ajaxDiv'>Your result will display here</div>

</body>
</html>
