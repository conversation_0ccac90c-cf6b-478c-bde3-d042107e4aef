<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID"
         xmlns="http://java.sun.com/xml/ns/j2ee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/j2ee http://java.sun.com/xml/ns/j2ee/web-app_2_4.xsd"
         version="3.1">

	<display-name>OOMA Dashboard Server</display-name>

    <!-- This must be included to enable us to explicitly order the listener -->
    <context-param>
        <param-name>isLog4jAutoInitializationDisabled</param-name>
        <param-value>true</param-value>
    </context-param>

    <filter>
        <filter-name>log4jServletFilter</filter-name>
        <filter-class>org.apache.logging.log4j.web.Log4jServletFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>log4jServletFilter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
        <dispatcher>INCLUDE</dispatcher>
        <dispatcher>ERROR</dispatcher>
        <dispatcher>ASYNC</dispatcher><!-- Servlet 3.0 w/ disabled auto-initialization only; not supported in 2.5 -->
    </filter-mapping>

    <!-- This listener should be the first ServletContextListener to ensure that it is -->
    <!-- initialized before others shut down AFTER others                              -->
    <listener>
        <listener-class>org.apache.logging.log4j.web.Log4jServletContextListener</listener-class>
    </listener>

    <listener>
		<listener-class>org.apache.shiro.web.env.EnvironmentLoaderListener</listener-class>
	</listener>

    <!-- This listener must be included to enable "before shutdown" CDI 1.x processing -->
    <listener>
        <listener-class>com.ticomgeo.jee.lifecycle.BeforeShutdownServletContextListener</listener-class>
    </listener>

    <filter>
        <filter-name>ShiroFilter</filter-name>
        <filter-class>org.apache.shiro.web.servlet.ShiroFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>ShiroFilter</filter-name>
        <url-pattern>/*</url-pattern>
        <dispatcher>REQUEST</dispatcher>
        <dispatcher>FORWARD</dispatcher>
        <dispatcher>INCLUDE</dispatcher>
        <dispatcher>ERROR</dispatcher>
    </filter-mapping>

	<context-param>
		<param-name>shiroConfigLocations</param-name>
		<param-value>classpath:dashboard-server-shiro.ini</param-value>
	</context-param>

    <filter>
        <filter-name>CacAuthenticationFilter</filter-name>
        <filter-class>com.ticomgeo.service.rest.dashboard.CacAuthenticationFilter</filter-class>
    </filter>

    <filter-mapping>
        <filter-name>CacAuthenticationFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

	<welcome-file-list>
		<welcome-file>content/ep3remoting.html</welcome-file>
	</welcome-file-list>

	<servlet>
		<servlet-name>LogoutServlet</servlet-name>
		<servlet-class>com.ticomgeo.service.rest.session.impl.LogoutServlet</servlet-class>
	</servlet>

	<servlet-mapping>
		<servlet-name>LogoutServlet</servlet-name>
		<url-pattern>/logout/*</url-pattern>
	</servlet-mapping>

    <!-- Reference resources for the infinispan caches that are referenced by our shiro configuration -->
    <!-- in lonestar-server-shiro.ini. This is done through resource-ref entries. The ini file        -->
    <!-- references a container name and cache names. The container name is "shiro", and the cache    -->
    <!-- names are "lonestar-authorization" for the authZ cache, and "lonestar-active-sessions" for   -->
    <!-- the active sessions cache. These container and cache names must be used also in the          -->
    <!-- lonestar-web-client-shiro.ini file for the corresponding container and cache names in order  -->
    <!-- for both to share the same security context information.                                     -->
    <!-- In addition to this resource-ref elements, we need "config-ext" scripts that allow us to     -->
    <!-- ensure that wildfly is configured to have these infinispan containers and caches at runtime  -->
    <resource-ref>
        <res-ref-name>infinispan/shiro-authz-cache</res-ref-name>
        <lookup-name>java:jboss/infinispan/cache/shiro/dashboard-authorization</lookup-name>
    </resource-ref>

    <resource-ref>
        <res-ref-name>infinispan/shiro-session-cache</res-ref-name>
        <lookup-name>java:jboss/infinispan/cache/shiro/dashboard-active-sessions</lookup-name>
    </resource-ref>

</web-app>
