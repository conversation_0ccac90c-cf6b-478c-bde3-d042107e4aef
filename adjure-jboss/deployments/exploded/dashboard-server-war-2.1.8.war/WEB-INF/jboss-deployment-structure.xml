<jboss-deployment-structure>
    <deployment>
        <dependencies>
            <module name="com.ticomgeo.dashboard.configuration" export="true"/>
            <module name="org.jboss.resteasy.resteasy-jaxrs" export="true"/>
            <module name="org.infinispan.commons" export="true"/>
            <module name="org.infinispan" export="true"/>
            <module name="org.jboss.as.clustering.infinispan" export="true"/>
            <module name="org.wildfly.clustering.infinispan.spi" export="true"/>
            <module name="org.apache.cxf" export="true"/>
            <module name="org.apache.cxf.impl" export="true"/>
            <module name="org.apache.shiro" export="true"/>
            <module name="org.apache.commons.io" export="true"/>
        </dependencies>
        <!-- exclude-subsystem prevents the deployment unit processor for a subsystem from running on a deployment -->
        <!-- which gives basically the same effect as removing the subsystem, but it only affects single deployment -->
        <exclude-subsystems>
            <subsystem name="logging"/>
        </exclude-subsystems>
    </deployment>
</jboss-deployment-structure>
