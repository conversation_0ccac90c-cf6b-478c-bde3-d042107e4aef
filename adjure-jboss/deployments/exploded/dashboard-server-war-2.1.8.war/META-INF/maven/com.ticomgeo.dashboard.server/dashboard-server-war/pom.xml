<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.ticomgeo.dashboard.server</groupId>
        <artifactId>dashboard-server-parent</artifactId>
        <version>2.1.8</version>
    </parent>

    <artifactId>dashboard-server-war</artifactId>
    <packaging>war</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <webResources>
                        <webResource>
                            <directory>src/main/webapp/WEB-INF</directory>
                            <targetPath>WEB-INF</targetPath>
                            <filtering>true</filtering>
                        </webResource>
                    </webResources>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-failsafe-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>integration-test</goal>
                            <goal>verify</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>analyze-deps</id>
                        <phase>verify</phase>
                        <goals>
                            <goal>analyze-only</goal>
                        </goals>
                        <configuration>
                            <failOnWarning>true</failOnWarning>
                            <usedDependencies>
                                <usedDependency>com.ticomgeo.sso:tgi-ssoutilities</usedDependency>
                                <usedDependency>${groupId.shiro-auth}:tgi-shiro-util</usedDependency>
                                <usedDependency>${groupId.shiro-auth}:tgi-shiro-web-env</usedDependency>
                                <usedDependency>${groupId.shiro-auth}:tgi-shiro-simpleidp</usedDependency>
                                <usedDependency>${groupId.shiro-auth}:tgi-shiro-cache-infinispan</usedDependency>
                                <usedDependency>org.bouncycastle:bcpkix-jdk15on</usedDependency>

                                <!-- add logging dependencies -->
                                <usedDependency>commons-logging:commons-logging</usedDependency>
                                <usedDependency>org.slf4j:slf4j-api</usedDependency>
                                <usedDependency>${groupId.log4j2}:log4j-1.2-api</usedDependency>
                                <usedDependency>${groupId.log4j2}:log4j-slf4j-impl</usedDependency>
                                <usedDependency>${groupId.log4j2}:log4j-jcl</usedDependency>
                                <usedDependency>${groupId.log4j2}:log4j-core</usedDependency>
                                <usedDependency>${groupId.log4j2}:log4j-web</usedDependency>
                                <usedDependency>log4j:log4j</usedDependency>

                                <usedDependency>${groupId.java-common}:java-util</usedDependency>

                                <!-- going to be excluded here, so that we can pull it in the EAR for java-util access -->
                                <usedDependency>com.google.protobuf:protobuf-java</usedDependency>

                                <!-- force pulling in of Settings interfaces/classes from Adjure, jee-framework, and java-common -->
                                <usedDependency>${groupId.java-common}:java-common-cfg-impl</usedDependency>
                                <usedDependency>${groupId.jee-framework}:jee-framework-cfg-impl</usedDependency>
                                <usedDependency>${groupId.adjure.core}:adjure-cfg-impl</usedDependency>

                                <!-- binding of service client & provider messaging to JMS -->
                                <usedDependency>${groupId.jee-framework}:service-messaging-jms</usedDependency>
                                <usedDependency>${groupId.jee-framework}:jee-messaging-jms</usedDependency>
                                <usedDependency>${groupId.jee-framework}:jms-messaging-qpid</usedDependency>
                                <!-- binding of transaction model, async, timer, startup interface to JEE (EJB) -->
                                <usedDependency>${groupId.jee-framework}:${artifactId.transaction-binding}</usedDependency>

                                <!-- force use of proto generated code compiled with ${protobuf.tools.bind.version} -->
                                <usedDependency>${groupId.adjure.core}:adjure-gpb-${protobuf.tools.bind.version}</usedDependency>

                                <!-- include rest API support modules -->
                                <usedDependency>${groupId.adjure.core}:app-service-rest</usedDependency>
                                <usedDependency>${groupId.adjure.core}:config-classification</usedDependency>
                                <usedDependency>${groupId.adjure.core}:session-service-access-rest</usedDependency>
                                <usedDependency>${groupId.adjure.core}:session-service-api</usedDependency>
                                <usedDependency>${groupId.adjure.core}:session-service-proxy</usedDependency>

                                <!-- binding to jboss container -->
                                <usedDependency>${groupId.adjure.platform}:wildfly-deployment-binding-common
                                </usedDependency>

                                <!-- application binding -->
                                <usedDependency>${project.groupId}:dashboard-server-war-binding</usedDependency>
                                <usedDependency>${project.groupId}:dashboard-server-access-rest</usedDependency>
                                <usedDependency>${project.groupId}:synchronizer</usedDependency>
                            </usedDependencies>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.wildfly.plugins</groupId>
                <artifactId>wildfly-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <!-- reference shiro dependencies -->
        <dependency>
            <groupId>com.ticomgeo.sso</groupId>
            <artifactId>tgi-ssoutilities</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-util</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-web-env</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-simpleidp</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-cache-infinispan</artifactId>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>

        <!-- logging dependencies -->
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.log4j2}</groupId>
            <artifactId>log4j-1.2-api</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.log4j2}</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.log4j2}</groupId>
            <artifactId>log4j-jcl</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.log4j2}</groupId>
            <artifactId>log4j-core</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.log4j2}</groupId>
            <artifactId>log4j-web</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <!--
               Pull in the version we want for our WAR (which may be different than the version used to compile some
               components).
              -->
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.java-common}</groupId>
            <artifactId>java-util</artifactId>
        </dependency>

        <!-- force inclusion of desired jars -->
        <dependency>
            <groupId>${groupId.java-common}</groupId>
            <artifactId>java-common-cfg-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>jee-framework-cfg-impl</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>service-messaging-jms</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>jee-messaging-jms</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>jms-messaging-qpid</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.jee-framework}</groupId>
            <artifactId>${artifactId.transaction-binding}</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>adjure-cfg-impl</artifactId>
        </dependency>
        
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>adjure-gpb-${protobuf.tools.bind.version}</artifactId>
            <version>${version.adjure.core}</version>
        </dependency>

        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>app-service-rest</artifactId>
            <version>${version.adjure.core}</version>
        </dependency>
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>config-classification</artifactId>
            <version>${version.adjure.core}</version>
        </dependency>
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>session-service-access-rest</artifactId>
            <version>${version.adjure.core}</version>
        </dependency>
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>session-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.adjure.core}</groupId>
            <artifactId>session-service-proxy</artifactId>
        </dependency>

        <!-- binding to wildfly container -->
        <dependency>
            <groupId>${groupId.adjure.platform}</groupId>
            <artifactId>wildfly-deployment-binding-common</artifactId>
            <version>${version.adjure.platform}</version>
        </dependency>

        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>dashboard-server-war-binding</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>dashboard-server-access-rest</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>synchronizer</artifactId>
            <version>${project.version}</version>
        </dependency>

    </dependencies>
</project>
