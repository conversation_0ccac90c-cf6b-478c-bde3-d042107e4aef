(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{1562:function(e,t,n){(e.exports=n(23)(!1)).push([e.i,".main{width:100%;height:100%;display:flex;flex-direction:column}.stack-container{flex:1 0 100%;display:flex;flex-direction:column;align-items:center;justify-content:center}.stack-container>.bp3-panel-stack-view{background:transparent}.stack-container .bp3-panel-stack-header{display:none}.stack-container>.bp3-panel-stack-appear,.stack-container>.bp3-panel-stack-enter{opacity:0}.stack-container>.bp3-panel-stack-appear-active,.stack-container>.bp3-panel-stack-enter-active,.stack-container>.bp3-panel-stack-enter-done{opacity:1;transition:opacity .2s ease-in}.stack-container>.bp3-panel-stack-exit{opacity:1}.stack-container>.bp3-panel-stack-exit-active,.stack-container>.bp3-panel-stack-exit-done{transform:unset;opacity:0;transition:opacity .2s ease-out}",""])},1563:function(e,t,n){var r=n(1562);"string"==typeof r&&(r=[[e.i,r,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};n(28)(r,a);r.locals&&(e.exports=r.locals)},1564:function(e,t,n){(e.exports=n(23)(!1)).push([e.i,".request-complete-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.request-complete-container>*{width:500px}.complete-request-header{display:flex;flex-direction:column;justify-content:start;align-items:stretch;font-size:22px}.complete-request-header.layout-item-header-2>*{text-align:left;margin-top:0;margin-bottom:0}.request-complete-border-box{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px 5px;font-size:20px}.request-complete-message{margin-top:20px}.request-complete-content{margin-top:50px;padding-left:10px;padding-right:10px;max-width:100%}",""])},1565:function(e,t,n){var r=n(1564);"string"==typeof r&&(r=[[e.i,r,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};n(28)(r,a);r.locals&&(e.exports=r.locals)},1566:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=u(n(5)),a=u(n(18)),o=n(838),l=n(850),i=u(n(840));function u(e){return e&&e.__esModule?e:{default:e}}function s(e){var t=e.succeed,n=void 0;return n=t?r.default.createElement(r.default.Fragment,null,r.default.createElement(i.default,{icon:l.IconNames.TICK_CIRCLE,iconSize:90,intent:o.Intent.SUCCESS}),r.default.createElement("h2",{className:"request-complete-message"},"Request Sent"),r.default.createElement("p",{className:"request-complete-content"},"Your access request has been sent for approval to an NARD administrator.")):r.default.createElement(r.default.Fragment,null,r.default.createElement(i.default,{icon:l.IconNames.ERROR,iconSize:90,intent:o.Intent.DANGER}),r.default.createElement("h2",{className:"request-complete-message"},"Request Failed"),r.default.createElement("p",{className:"request-complete-content"},"We were unable to issue your access request.",r.default.createElement("br",null),"Contact an administrator and try again.")),r.default.createElement("div",{className:"request-complete-container"},r.default.createElement("div",{className:"complete-request-header layout-item-header-2"},r.default.createElement("div",{className:"complete-request-header-text"},"Request ",t?"Sent":"Failed")),r.default.createElement("div",{className:"request-complete-border-box border-uline-light-medium"},n))}n(1565),s.propTypes={succeed:a.default.bool},s.defaultProps={succeed:!0},t.default=s},1567:function(e,t,n){(e.exports=n(23)(!1)).push([e.i,".request-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.request-container>*{width:500px}.access-request-header{display:flex;flex-direction:column;justify-content:start;align-items:stretch;font-size:22px}.access-request-header.layout-item-header-2>*{text-align:left;margin-top:0;margin-bottom:0}.request-border-box{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px 5px;font-size:20px}.request-border-box>*{width:50%}.field-title:first-child{margin-top:10px}.field-title{margin-top:20px;margin-bottom:5px}.uneditable{font-size:20px;background-color:#6b7479;width:50%;padding-left:5px}.send-request-button{margin-top:30px}",""])},1568:function(e,t,n){var r=n(1567);"string"==typeof r&&(r=[[e.i,r,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};n(28)(r,a);r.locals&&(e.exports=r.locals)},1569:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.load=void 0;var r=l(n(12)),a=l(n(329)),o=n(128);n(213);function l(e){return e&&e.__esModule?e:{default:e}}var i,u,s=(i=r.default.mark(function e(){var t,n,l;return r.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return t=void 0,e.prev=1,e.next=4,a.default.get(o.UNAUTHENTICATED_SQUADRON_LIST);case 4:n=e.sent,l=n.json,t=l,e.next=12;break;case 9:e.prev=9,e.t0=e.catch(1),t=[];case 12:return e.abrupt("return",t);case 13:case"end":return e.stop()}},e,void 0,[[1,9]])}),u=function(){var e=i.apply(this,arguments);return new Promise(function(t,n){return function r(a,o){try{var l=e[a](o),i=l.value}catch(e){return void n(e)}if(!l.done)return Promise.resolve(i).then(function(e){r("next",e)},function(e){r("throw",e)});t(i)}("next")})},function(){return u.apply(this,arguments)});t.load=s},1584:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=y(n(12)),a=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,a=!1,o=void 0;try{for(var l,i=e[Symbol.iterator]();!(r=(l=i.next()).done)&&(n.push(l.value),!t||n.length!==t);r=!0);}catch(e){a=!0,o=e}finally{try{!r&&i.return&&i.return()}finally{if(a)throw o}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),o=n(5),l=y(o),i=y(n(18)),u=n(838),s=y(n(911)),c=y(n(909)),d=n(320),f=n(206),p=n(1569),m=y(n(885)),v=n(317);function y(e){return e&&e.__esModule?e:{default:e}}function h(e){return function(){var t=e.apply(this,arguments);return new Promise(function(e,n){return function r(a,o){try{var l=t[a](o),i=l.value}catch(e){return void n(e)}if(!l.done)return Promise.resolve(i).then(function(e){r("next",e)},function(e){r("throw",e)});e(i)}("next")})}}function x(e){var t=this,n=e.user,i=n.firstName,y=n.middleName,x=n.lastName,b=n.dodIdNumber,g=(0,o.useState)(null),E=a(g,2),N=E[0],q=E[1],S=(0,o.useState)(!1),_=a(S,2),R=_[0],A=_[1],k=(0,o.useState)(null),w=a(k,2),C=w[0],P=w[1],I=(0,o.useState)({}),O=a(I,2),j=O[0],T=O[1],D=(0,o.useState)(!1),M=a(D,2),z=M[0],L=M[1],U=(0,o.useState)([]),V=a(U,2),H=V[0],K=V[1],G=(0,o.useRef)(!1);(0,o.useEffect)(function(){if(N){var e,n=(e=h(r.default.mark(function e(){var n;return r.default.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,p.load)();case 2:n=e.sent,G.current=!0,K(n);case 5:case"end":return e.stop()}},e,t)})),function(){return e.apply(this,arguments)});(0,d.roleHasAccess)(N.id,f.VIEW_ALL_SQUADRONS)?A(!0):(A(!1),G.current||n())}else A(!1)},[N]);var W,B=(W=h(r.default.mark(function a(){var o,l,i,u,s,c,p;return r.default.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(r=void 0,a=void 0,r={},a=!1,N||(r.role="Select a desired role.",a=!0),!N||(0,d.roleHasAccess)(N.id,f.VIEW_ALL_SQUADRONS)||C||(r.squadron="Select a squadron for this role.",a=!0),T(a?r:{}),!!a){t.next=13;break}return L(!0),o=void 0,l=void 0,i=void 0,(0,d.roleHasAccess)(N.id,f.VIEW_ALL_SQUADRONS)||(o=C.squadronId,l=C.orgCode,i=o+"-"+l),u={role:N.id,squadronKey:i,squadronId:o,orgCode:l,emailAddress:n.emailAddress,lastName:n.lastName,firstName:n.firstName,middleName:n.middleName,phone:n.phone},t.next=10,(0,v.requestAccess)(u);case 10:s=t.sent,c=e.openPanel,"function"==typeof(p=e.onRequestComplete)&&p(c,s);case 13:case"end":return t.stop()}var r,a},a,t)})),function(){return W.apply(this,arguments)}),F=void 0;i||x?(F=i,y&&(F=F+" "+y),F=F+" "+x):F="-- Unknown --";var Y=void 0;return Y=N?R?l.default.createElement("input",{className:"bp3-input disabled",type:"text",value:"All",readOnly:!0}):l.default.createElement(u.Tooltip,{isOpen:!!j.squadron,intent:u.Intent.DANGER,content:j.squadron,position:u.Position.RIGHT,transitionDuration:0},l.default.createElement(c.default,{squadrons:H,defaultSelection:C,onChange:function(e){if(j.squadron){var t=Object.assign({},j);delete t.squadron,T(t)}P(e)}})):l.default.createElement("input",{className:"bp3-input disabled",type:"text",value:"-- Select Role --",readOnly:!0}),l.default.createElement("div",{className:"request-container"},l.default.createElement("div",{className:"access-request-header layout-item-header-2"},l.default.createElement("div",{className:"access-request-header-text"},"Request Access")),l.default.createElement("div",{className:"request-border-box border-uline-light-medium"},l.default.createElement("div",{className:"field-title"},"Name"),l.default.createElement("input",{className:"bp3-input disabled",type:"text",value:""+F,readOnly:!0}),l.default.createElement("div",{className:"field-title"},"ID Number"),l.default.createElement("input",{className:"bp3-input disabled",type:"text",value:b,readOnly:!0}),l.default.createElement("div",{className:"field-title"},"Role"),l.default.createElement(u.Tooltip,{isOpen:!!j.role,intent:u.Intent.DANGER,content:j.role,position:u.Position.RIGHT},l.default.createElement(s.default,{roles:m.default,onRoleChange:function(e){if(j.role){var t=Object.assign({},j);delete t.role,T(t)}q(e)}})),l.default.createElement("div",{className:"field-title"},"Squadrons"),Y,l.default.createElement("div",{className:"send-request-button"},l.default.createElement(u.Button,{className:"bp3-intent-primary",text:"Send Request",onClick:B,loading:z}))))}n(1568),x.propTypes={user:i.default.object,onRequestComplete:i.default.func,openPanel:i.default.func.isRequired},x.defaultProps={user:{},onRequestSucceed:null},t.default=x},1585:function(e,t,n){(e.exports=n(23)(!1)).push([e.i,".error-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.error-title{font-size:6em;color:red;text-shadow:-1px 1px 0 #fff,1px 1px 0 #fff,1px -1px 0 #fff,-1px -1px 0 #fff}.request-access-button{margin-top:2em}",""])},1586:function(e,t,n){var r=n(1585);"string"==typeof r&&(r=[[e.i,r,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};n(28)(r,a);r.locals&&(e.exports=r.locals)},1587:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i(n(5)),a=i(n(18)),o=n(838);n(1586);var l=function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}(n(328));function i(e){return e&&e.__esModule?e:{default:e}}c.propTypes={errorType:a.default.string,onRequest:a.default.func.isRequired,openPanel:a.default.func.isRequired},c.defaultProps={errorType:l.UNKNOWN_CAC};var u=function(e){switch(e){case l.APPROVAL_DENIED:return"Your approval request has been denied.";case l.PENDING_APPROVAL:return"Your approval request is pending.";case l.CAC_REVOKED:return"Your CAC access has been revoked.";default:return"You do not have authorization to view this page."}},s=function(e){switch(e){case l.APPROVAL_DENIED:case l.PENDING_APPROVAL:case l.CAC_REVOKED:return!1;default:return!0}};function c(e){var t=e.errorType,n=e.onRequest,a=e.openPanel,l=u(t),i=s(t);return r.default.createElement("div",{className:"error-container"},r.default.createElement("div",{className:"error-content-wrapper"},r.default.createElement("h1",{className:"error-title"},"Access Denied"),r.default.createElement("h2",null,l),i?r.default.createElement(o.Button,{className:"request-access-button bp3-intent-primary",text:"Request Access",onClick:function(){n(a)}}):null))}t.default=c},832:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=f(n(5)),a=f(n(18)),o=n(837),l=n(838),i=f(n(6)),u=f(n(912)),s=f(n(1587)),c=f(n(1584)),d=f(n(1566));function f(e){return e&&e.__esModule?e:{default:e}}function p(e){var t=e.error,n=t.user,a=t.type,f=function(e,t){var n=!!t;e({component:d.default,title:"Request "+(n?"Sent":"Failed"),props:{user:t,succeed:n}})};return r.default.createElement(o.Provider,{store:i.default.store},r.default.createElement("div",{className:"main bp3-dark"},r.default.createElement(u.default,null),r.default.createElement(l.PanelStack,{initialPanel:{component:s.default,title:"Access Denied",props:{errorType:a,onRequest:function(e,t){e({component:c.default,title:"Request Access",props:{user:n,onRequestComplete:f}})}}},className:"stack-container",showPanelHeader:!1})))}n(1563),p.propTypes={error:a.default.object},p.defaultProps={error:{}},t.default=p},840:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i(n(5)),a=i(n(18)),o=n(838),l=n(908);function i(e){return e&&e.__esModule?e:{default:e}}var u="fcf_icon";function s(e){var t=e.className,n=e.color,a=e.htmltitle,i=e.icon,s=e.iconSize,c=e.intent,d=e.style,f=e.tagName,p=e.title;if(!e.custom)return r.default.createElement(o.Icon,{className:t,color:n,htmltitle:a,icon:i,iconSize:s,intent:c,style:d,tagName:f,title:p});switch(i){case u:return r.default.createElement(l.ReactComponent,{style:d});default:return r.default.createElement("div",null)}}s.propTypes={className:a.default.string,color:a.default.string,htmltitle:a.default.string,icon:a.default.string,iconSize:a.default.number,intent:a.default.string,style:a.default.object,tagName:a.default.string,title:a.default.string,custom:a.default.bool},s.defaultProps={className:"",color:void 0,htmltitle:void 0,icon:"",iconSize:16,intent:"",style:{fill:"white"},tagName:void 0,title:"",custom:!1},t.default=s},858:function(e,t,n){var r=n(910);"string"==typeof r&&(r=[[e.i,r,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};n(28)(r,a);r.locals&&(e.exports=r.locals)},885:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});t.default=[].concat([{id:1,name:"Dashboard Administrator"},{id:2,name:"Command User"},{id:3,name:"Squadron Administrator"},{id:4,name:"Squadron User"}])},908:function(e,t,n){"use strict";n.r(t),n.d(t,"ReactComponent",function(){return u});var r=n(5),a=n.n(r);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}var l=a.a.createElement("path",{d:"M420.498 65.192c-48.503-8.692-93.169-35.18-115.477-50.195C290.446 5.186 273.495 0 255.999 0s-34.447 5.186-49.021 14.997C184.67 30.012 140.004 56.501 91.502 65.193 62.776 70.34 41.926 95.195 41.926 124.29v120.709c0 39.877 11.156 78.75 33.159 115.539 17.213 28.781 41.064 56.288 70.888 81.757 50.147 42.825 99.803 65.157 101.892 66.086l8.134 3.619 8.136-3.619c2.089-.929 51.745-23.261 101.892-66.086 29.823-25.469 53.674-52.976 70.888-81.757 22.003-36.79 33.159-75.662 33.159-115.539v-120.71c0-29.095-20.85-53.95-49.576-59.097zm9.56 179.806c0 59.45-30.033 115.375-89.267 166.224-34.432 29.558-69.39 48.824-84.791 56.643-35.401-17.987-174.061-96.536-174.061-222.866v-120.71c0-9.695 6.99-17.985 16.621-19.711 55.718-9.985 105.843-39.615 130.761-56.387 7.947-5.349 17.173-8.177 26.678-8.177 9.505 0 18.73 2.828 26.677 8.178 24.919 16.772 75.043 46.402 130.761 56.388 9.63 1.726 16.621 10.015 16.621 19.711v120.707z"}),i=a.a.createElement("path",{d:"M225.014 294.431l-68.38-64.018-27.348 29.21 98.307 92.036 156.376-175.354-29.864-26.632z"}),u=function(e){return a.a.createElement("svg",o({viewBox:"0 0 512 512"},e),l,i)};t.default=n.p+"fonts/5360b6db.svg"},909:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,a=!1,o=void 0;try{for(var l,i=e[Symbol.iterator]();!(r=(l=i.next()).done)&&(n.push(l.value),!t||n.length!==t);r=!0);}catch(e){a=!0,o=e}finally{try{!r&&i.return&&i.return()}finally{if(a)throw o}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=n(5),o=c(a),l=c(n(18)),i=n(853),u=n(838),s=n(850);function c(e){return e&&e.__esModule?e:{default:e}}function d(e){var t=e.squadrons,n=e.onChange,l=e.defaultSelection,c=(0,a.useState)(l),d=r(c,2),f=d[0],p=d[1];(0,a.useEffect)(function(){l&&f&&l.squadronKey!==f.squadronKey&&p(l)},[l]);return o.default.createElement(i.Select,{items:t,itemRenderer:function(e,t){var n=t.handleClick,r=f===e?"selected":"";return o.default.createElement(u.MenuItem,{key:e.squadronKey,text:e.name,onClick:n,className:r})},onItemSelect:function(e){p(e),"function"==typeof n&&n(e)},filterable:!1},o.default.createElement(u.Button,{rightIcon:s.IconNames.CARET_DOWN,text:f?f.name:"---"}))}n(858),d.propTypes={squadrons:l.default.array,onChange:l.default.func,defaultSelection:l.default.object},d.defaultProps={squadrons:[],onChange:null,defaultSelection:null},t.default=d},910:function(e,t,n){(e.exports=n(23)(!1)).push([e.i,".selected{background-color:rgba(138,155,168,.15)}",""])},911:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=function(){return function(e,t){if(Array.isArray(e))return e;if(Symbol.iterator in Object(e))return function(e,t){var n=[],r=!0,a=!1,o=void 0;try{for(var l,i=e[Symbol.iterator]();!(r=(l=i.next()).done)&&(n.push(l.value),!t||n.length!==t);r=!0);}catch(e){a=!0,o=e}finally{try{!r&&i.return&&i.return()}finally{if(a)throw o}}return n}(e,t);throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),a=n(5),o=c(a),l=c(n(18)),i=n(853),u=n(838),s=n(850);function c(e){return e&&e.__esModule?e:{default:e}}function d(e){var t=e.roles,n=e.defaultSelection,l=e.onRoleChange,c=(0,a.useState)(n),d=r(c,2),f=d[0],p=d[1];(0,a.useEffect)(function(){n&&n.id!==f.id&&p(n)},[n]);return o.default.createElement(i.Select,{items:t,itemRenderer:function(e,t){var n=t.handleClick,r=f===e?"selected":"";return o.default.createElement(u.MenuItem,{key:e.id,text:e.name,onClick:n,className:r})},onItemSelect:function(e){p(e),"function"==typeof l&&l(e)},filterable:!1},o.default.createElement(u.Button,{rightIcon:s.IconNames.CARET_DOWN,text:f?f.name:"Select Role"}))}n(858),d.propTypes={roles:l.default.array,defaultSelection:l.default.object,onRoleChange:l.default.func},d.defaultProps={roles:[],defaultSelection:null,onRoleChange:null},t.default=d},912:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=i(n(5)),a=n(837),o=i(n(18)),l=n(326);function i(e){return e&&e.__esModule?e:{default:e}}function u(e){var t=e.classification,n={color:t.foregroundColor,backgroundColor:t.backgroundColor,fontSize:e.fontSize,textAlign:e.textAlign,height:"25px",minHeight:"25px",cursor:"default"},a=t.marking;return r.default.createElement("div",{style:n,id:"classificationBanner"},a)}u.propTypes={classification:o.default.object.isRequired,fontSize:o.default.string,textAlign:o.default.string},u.defaultProps={fontSize:"1.3em",textAlign:"center"},t.default=(0,a.connect)(function(e){return{classification:e[l.classificationSliceName]}})(u)}}]);
//# sourceMappingURL=3.js.map