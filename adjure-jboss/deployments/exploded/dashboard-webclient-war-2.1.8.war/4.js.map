{"version": 3, "sources": ["webpack:///./src/loaderror.scss", "webpack:///./src/loaderror.scss?7e3b", "webpack:///./src/LoadError.js"], "names": ["module", "exports", "__webpack_require__", "push", "i", "content", "options", "hmr", "transform", "insertInto", "undefined", "locals", "LoadError", "props", "message", "_react2", "default", "createElement", "className", "propTypes", "PropTypes", "string", "isRequired"], "mappings": "+EAAAA,EAAAC,QAAAC,EAAA,GAAAA,EAAA,IAKAC,MAAAH,EAAAI,EAAA,wIAA+J,2BCJ/J,IAAAC,EAAAH,EAAA,MAEA,iBAAAG,QAAAL,EAAAI,EAAAC,EAAA,MAOA,IAAAC,GAAeC,KAAA,EAEfC,eAPAA,EAQAC,gBAAAC,GAEAR,EAAA,GAAAA,CAAAG,EAAAC,GAEAD,EAAAM,SAAAX,EAAAC,QAAAI,EAAAM,2FCjBA,QAAAT,EAAA,QACAA,EAAA,wDAIA,SAASU,EAAUC,GAAO,IAChBC,EAAYD,EAAZC,QACR,OACEC,EAAAC,QAAAC,cAAA,OAAKC,UAAU,mBACXH,EAAAC,QAAAC,cAAA,2CACAF,EAAAC,QAAAC,cAAA,KAAGC,UAAU,aAAaJ,IAPlCZ,EAAA,MAYAU,EAAUO,WACRL,QAASM,UAAUC,OAAOC,sBAGbV", "file": "4.js", "sourcesContent": ["exports = module.exports = require(\"../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".error-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.error-msg{font-size:1.5em}\", \"\"]);\n\n// exports\n", "\nvar content = require(\"!!../node_modules/css-loader/index.js??ref--9-1!../node_modules/sass-loader/lib/loader.js!./loaderror.scss\");\n\nif(typeof content === 'string') content = [[module.id, content, '']];\n\nvar transform;\nvar insertInto;\n\n\n\nvar options = {\"hmr\":true}\n\noptions.transform = transform\noptions.insertInto = undefined;\n\nvar update = require(\"!../node_modules/style-loader/lib/addStyles.js\")(content, options);\n\nif(content.locals) module.exports = content.locals;\n\nif(module.hot) {\n\tmodule.hot.accept(\"!!../node_modules/css-loader/index.js??ref--9-1!../node_modules/sass-loader/lib/loader.js!./loaderror.scss\", function() {\n\t\tvar newContent = require(\"!!../node_modules/css-loader/index.js??ref--9-1!../node_modules/sass-loader/lib/loader.js!./loaderror.scss\");\n\n\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\n\t\tvar locals = (function(a, b) {\n\t\t\tvar key, idx = 0;\n\n\t\t\tfor(key in a) {\n\t\t\t\tif(!b || a[key] !== b[key]) return false;\n\t\t\t\tidx++;\n\t\t\t}\n\n\t\t\tfor(key in b) idx--;\n\n\t\t\treturn idx === 0;\n\t\t}(content.locals, newContent.locals));\n\n\t\tif(!locals) throw new Error('Aborting CSS HMR due to changed css-modules locals.');\n\n\t\tupdate(newContent);\n\t});\n\n\tmodule.hot.dispose(function() { update(); });\n}", "import React from 'react';\r\nimport PropTypes from 'prop-types';\r\n\r\nimport './loaderror.scss';\r\n\r\nfunction LoadError(props) {\r\n  const { message } = props;\r\n  return (\r\n    <div className=\"error-container\">\r\n        <h1>Unable to load NARD Dashboard</h1>\r\n        <p className=\"error-msg\">{message}</p>\r\n    </div>\r\n  );\r\n}\r\n\r\nLoadError.propTypes = {\r\n  message: PropTypes.string.isRequired\r\n};\r\n\r\nexport default LoadError;\r\n"], "sourceRoot": ""}