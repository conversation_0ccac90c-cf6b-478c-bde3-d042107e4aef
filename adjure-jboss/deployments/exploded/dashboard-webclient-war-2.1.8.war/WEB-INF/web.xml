<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         version="2.5">

    <display-name>OOMA Dashboard Web Client</display-name>

  	<listener>
  		<listener-class>org.apache.shiro.web.env.EnvironmentLoaderListener</listener-class>
  	</listener>

  	<filter>
    		<filter-name>ShiroFilter</filter-name>
    		<filter-class>org.apache.shiro.web.servlet.ShiroFilter</filter-class>
  	</filter>

  	<filter-mapping>
    		<filter-name>ShiroFilter</filter-name>
    		<url-pattern>/*</url-pattern>
    		<dispatcher>REQUEST</dispatcher> 
    		<dispatcher>FORWARD</dispatcher> 
    		<dispatcher>INCLUDE</dispatcher> 
    		<dispatcher>ERROR</dispatcher>
  	</filter-mapping>

  	<context-param>
    		<param-name>shiroConfigLocations</param-name>
    		<param-value>classpath:dashboard-webclient-shiro.ini</param-value>
  	</context-param>

    <!-- Default page to serve -->
    <welcome-file-list>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

    <!-- Filters that set http response cache headers -->
    <!-- CacheTime in seconds -->
    <filter>
        <filter-name>CacheFilterFourWeeks</filter-name>
        <filter-class>com.ticomgeo.servlet.filter.CacheHeaderFilter</filter-class>
        <init-param>
            <param-name>CacheTime</param-name>
            <param-value>2419200</param-value>
        </init-param>
    </filter>

    <filter>
        <filter-name>CacheFilterOneWeek</filter-name>
        <filter-class>com.ticomgeo.servlet.filter.CacheHeaderFilter</filter-class>
        <init-param>
            <param-name>CacheTime</param-name>
            <param-value>604800</param-value>
        </init-param>
    </filter>

    <filter>
        <filter-name>CharSetFilterUTF</filter-name>
        <filter-class>com.ticomgeo.servlet.filter.CharSetHeaderFilter</filter-class>
        <init-param>
            <param-name>CharSet</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>

    <filter-mapping>
        <filter-name>CharSetFilterUTF</filter-name>
        <url-pattern>*.html</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CacheFilterFourWeeks</filter-name>
        <url-pattern>*.js</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CacheFilterFourWeeks</filter-name>
        <url-pattern>*.css</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CacheFilterFourWeeks</filter-name>
        <url-pattern>*.gif</url-pattern>
    </filter-mapping>

    <filter-mapping>
        <filter-name>CacheFilterFourWeeks</filter-name>
        <url-pattern>*.png</url-pattern>
    </filter-mapping>

</web-app>
