<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ticomgeo.dashboard.wc</groupId>
        <artifactId>dashboard-wc-parent</artifactId>
        <version>2.1.8</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>dashboard-webclient-war</artifactId>
    <name>OOMS-Branded Dashboard Web Client War</name>
    <description>OOMA-Branded Dashboard Web Client War</description>
    <packaging>war</packaging>

    <properties>
        <skip.verify.convergence.deps>true</skip.verify.convergence.deps>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.ticomgeo</groupId>
            <artifactId>servlet-filters</artifactId>
        </dependency>
        <dependency>
            <groupId>${project.groupId}</groupId>
            <artifactId>dashboard-wc-branding</artifactId>
            <version>${project.version}</version>
            <type>zip</type>
            <classifier>source</classifier>
        </dependency>
        <dependency>
            <groupId>com.ticomgeo.dashboard</groupId>
            <artifactId>dashboard-webclient</artifactId>
            <classifier>prod</classifier>
            <type>zip</type>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.ticomgeo.sso</groupId>
            <artifactId>tgi-ssoutilities</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-util</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-web-env</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-simpleidp</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-ssosepep</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-provider</artifactId>
        </dependency>
        <dependency>
            <groupId>${groupId.shiro-auth}</groupId>
            <artifactId>tgi-shiro-cache-infinispan</artifactId>
        </dependency>

        <!-- Third Party -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jaxrs</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcpkix-jdk15on</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>dashboard-webclient</finalName>

        <plugins>
            <!-- Package the war file. -->
            <plugin>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Dependencies>com.ticomgeo.dashboard.configuration,org.apache.httpcomponents,org.infinispan export,
                                org.apache.shiro export, org.infinispan.commons export, org.jboss.as.clustering.infinispan, org.slf4j
                                export, org.wildfly.clustering.infinispan.spi
                            </Dependencies>
                        </manifestEntries>
                    </archive>
                    <!-- Exclude Shiro libs from packaging, they're being picked up as a jboss-module -->
                    <packagingExcludes>WEB-INF/lib/shiro-*.jar</packagingExcludes>

                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ico</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                        <nonFilteredFileExtension>swf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>

                    <webResources>
                        <webResource>
                            <directory>src/main/webapp/WEB-INF</directory>
                            <targetPath>WEB-INF</targetPath>
                            <filtering>true</filtering>
                        </webResource>
                    </webResources>

                    <overlays>
                        <overlay>
                            <groupId>${project.groupId}</groupId>
                            <artifactId>dashboard-wc-branding</artifactId>
                            <type>zip</type>
                            <classifier>source</classifier>
                        </overlay>
                        <overlay>
                            <groupId>com.ticomgeo.dashboard</groupId>
                            <artifactId>dashboard-webclient</artifactId>
                            <classifier>prod</classifier>
                            <type>zip</type>
                        </overlay>
                    </overlays>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
