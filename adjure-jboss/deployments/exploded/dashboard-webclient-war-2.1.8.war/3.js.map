{"version": 3, "sources": ["webpack:///./src/components/Unauthorized/unauthorized.scss", "webpack:///./src/components/Unauthorized/unauthorized.scss?f0ef", "webpack:///./src/components/Unauthorized/requestcomplete.scss", "webpack:///./src/components/Unauthorized/requestcomplete.scss?01d7", "webpack:///./src/components/Unauthorized/RequestComplete.js", "webpack:///./src/components/Unauthorized/requestaccess.scss", "webpack:///./src/components/Unauthorized/requestaccess.scss?5007", "webpack:///./src/services/unauthenticated/squadronList.js", "webpack:///./src/components/Unauthorized/RequestAccess.js", "webpack:///./src/components/Unauthorized/accessdenied.scss", "webpack:///./src/components/Unauthorized/accessdenied.scss?8466", "webpack:///./src/components/Unauthorized/AccessDenied.js", "webpack:///./src/components/Unauthorized/Unauthorized.js", "webpack:///./src/components/AppIcon/AppIcon.js", "webpack:///./src/components/Selectors/selectors.scss?00e9", "webpack:///./src/utils/Roles.js", "webpack:///./src/images/icon_svgs/fcf_shield.svg", "webpack:///./src/components/Selectors/SquadronSelector.js", "webpack:///./src/components/Selectors/selectors.scss", "webpack:///./src/components/Selectors/RoleSelector.js", "webpack:///./src/components/layout/Classification/index.js"], "names": ["module", "exports", "__webpack_require__", "push", "i", "content", "options", "hmr", "transform", "insertInto", "undefined", "locals", "_core", "_icons", "RequestComplete", "props", "succeed", "messageComponent", "_react2", "default", "createElement", "Fragment", "_AppIcon2", "icon", "IconNames", "TICK_CIRCLE", "iconSize", "intent", "Intent", "SUCCESS", "className", "ERROR", "DANGER", "propTypes", "PropTypes", "bool", "defaultProps", "_Urls", "_ref", "load", "_regenerator2", "mark", "_callee", "squadrons", "_ref2", "json", "wrap", "_context", "prev", "next", "http", "get", "UNAUTHENTICATED_SQUADRON_LIST", "sent", "t0", "abrupt", "stop", "apply", "this", "arguments", "_react", "_authorization", "_AccessTypes", "_squadronList", "_user", "RequestAccess", "_this", "user", "firstName", "middleName", "lastName", "dodIdNumber", "_useState", "useState", "_useState2", "_slicedToArray", "selectedR<PERSON>", "setSelectedRole", "_useState3", "_useState4", "fullSquadronAccess", "setFullSquadronAccess", "_useState5", "_useState6", "selectedSquadron", "setSelectedSquadron", "_useState7", "_useState8", "errors", "setErrors", "_useState9", "_useState10", "requesting", "setRequesting", "_useState11", "_useState12", "setSquadrons", "didLoadSquadronsRef", "useRef", "useEffect", "loadSquadrons", "_asyncToGenerator", "squadronList", "current", "roleHasAccess", "id", "VIEW_ALL_SQUADRONS", "sendRequest", "_callee2", "squadronId", "orgCode", "squadronKey", "requestingUser", "savedUser", "openPanel", "onRequestComplete", "_context2", "validateErrors", "hasErrors", "role", "squadron", "emailAddress", "phone", "requestAccess", "name", "squadronComponent", "type", "value", "readOnly", "<PERSON><PERSON><PERSON>", "isOpen", "position", "Position", "RIGHT", "transitionDuration", "_SquadronSelector2", "defaultSelection", "onChange", "newErrors", "_RoleSelector2", "roles", "onRoleChange", "<PERSON><PERSON>", "text", "onClick", "loading", "object", "func", "isRequired", "onRequestSucceed", "ErrorTypes", "AccessDenied", "errorType", "string", "onRequest", "UNKNOWN_CAC", "getMessageFromType", "APPROVAL_DENIED", "PENDING_APPROVAL", "CAC_REVOKED", "doesErrorAllowAccess", "message", "allowAccessRequest", "_reactRedux", "Unauthorized", "error", "requestedUser", "component", "title", "Provider", "store", "State", "_Classification2", "PanelStack", "initialPanel", "showPanelHeader", "_fcf_shield", "FCF_ICON", "AppIcon", "color", "htmltitle", "style", "tagName", "custom", "Icon", "ReactComponent", "number", "fill", "react__WEBPACK_IMPORTED_MODULE_0__", "react__WEBPACK_IMPORTED_MODULE_0___default", "n", "_extends", "Object", "assign", "target", "length", "source", "key", "prototype", "hasOwnProperty", "call", "a", "d", "SvgFcfShield", "viewBox", "__webpack_exports__", "p", "_select", "SquadronSelector", "selected", "setSelected", "Select", "items", "itemRenderer", "itemProps", "handleClick", "selectedClass", "MenuItem", "onItemSelect", "filterable", "rightIcon", "CARET_DOWN", "array", "RoleSelector", "_classification", "Classification", "classification", "bannerStyle", "foregroundColor", "backgroundColor", "fontSize", "textAlign", "height", "minHeight", "cursor", "marking", "connect", "state", "classificationSliceName"], "mappings": "+EAAAA,EAAAC,QAAAC,EAAA,GAAAA,EAAA,IAKAC,MAAAH,EAAAI,EAAA,wvBAA+wB,2BCJ/wB,IAAAC,EAAAH,EAAA,MAEA,iBAAAG,QAAAL,EAAAI,EAAAC,EAAA,MAOA,IAAAC,GAAeC,KAAA,EAEfC,eAPAA,EAQAC,gBAAAC,GAEAR,EAAA,GAAAA,CAAAG,EAAAC,GAEAD,EAAAM,SAAAX,EAAAC,QAAAI,EAAAM,+BCjBAX,EAAAC,QAAAC,EAAA,GAAAA,EAAA,IAKAC,MAAAH,EAAAI,EAAA,soBAA6pB,2BCJ7pB,IAAAC,EAAAH,EAAA,MAEA,iBAAAG,QAAAL,EAAAI,EAAAC,EAAA,MAOA,IAAAC,GAAeC,KAAA,EAEfC,eAPAA,EAQAC,gBAAAC,GAEAR,EAAA,GAAAA,CAAAG,EAAAC,GAEAD,EAAAM,SAAAX,EAAAC,QAAAI,EAAAM,4FCjBA,QAAAT,EAAA,QACAA,EAAA,KACAU,EAAAV,EAAA,KACAW,EAAAX,EAAA,SACAA,EAAA,yDAGA,SAASY,EAAgBC,GAAO,IACtBC,EAAYD,EAAZC,QAEJC,SAmBJ,OAjBEA,EADED,EAEAE,EAAAC,QAAAC,cAACF,EAAAC,QAAME,SAAP,KACEH,EAAAC,QAAAC,cAACE,EAAAH,SAAQI,KAAMC,YAAUC,YAAaC,SAAU,GAAIC,OAAQC,SAAOC,UACnEX,EAAAC,QAAAC,cAAA,MAAIU,UAAU,4BAAd,gBACAZ,EAAAC,QAAAC,cAAA,KAAGU,UAAU,4BAAb,6EAKFZ,EAAAC,QAAAC,cAACF,EAAAC,QAAME,SAAP,KACEH,EAAAC,QAAAC,cAACE,EAAAH,SAAQI,KAAMC,YAAUO,MAAOL,SAAU,GAAIC,OAAQC,SAAOI,SAC7Dd,EAAAC,QAAAC,cAAA,MAAIU,UAAU,4BAAd,kBACAZ,EAAAC,QAAAC,cAAA,KAAGU,UAAU,4BAAb,+CAAoFZ,EAAAC,QAAAC,cAAA,WAApF,4CAMJF,EAAAC,QAAAC,cAAA,OAAKU,UAAU,8BACbZ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,gDACbZ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,gCAAf,WAAuDd,EAAU,OAAS,WAE5EE,EAAAC,QAAAC,cAAA,OAAKU,UAAU,yDACZb,IA9BTf,EAAA,MAoCAY,EAAgBmB,WACdjB,QAASkB,UAAUC,MAGrBrB,EAAgBsB,cACdpB,SAAS,aAGIF,yBCjDfd,EAAAC,QAAAC,EAAA,GAAAA,EAAA,IAKAC,MAAAH,EAAAI,EAAA,2sBAAkuB,2BCJluB,IAAAC,EAAAH,EAAA,MAEA,iBAAAG,QAAAL,EAAAI,EAAAC,EAAA,MAOA,IAAAC,GAAeC,KAAA,EAEfC,eAPAA,EAQAC,gBAAAC,GAEAR,EAAA,GAAAA,CAAAG,EAAAC,GAEAD,EAAAM,SAAAX,EAAAC,QAAAI,EAAAM,6HCjBAT,EAAA,MACAmC,EAAAnC,EAAA,KACAA,EAAA,wDAEA,MAAMoC,EAAAC,KAAAC,EAAArB,QAAAsB,KAAO,SAAAC,IAAA,IAAAC,EAAAC,EAAAC,EAAA,OAAAL,EAAArB,QAAA2B,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cACPN,OADO,EAAAI,EAAAC,KAAA,EAAAD,EAAAE,KAAA,EAGcC,UAAKC,IAAIC,iCAHvB,OAAAR,EAAAG,EAAAM,KAGDR,EAHCD,EAGDC,KAERF,EAAYE,EALHE,EAAAE,KAAA,gBAAAF,EAAAC,KAAA,EAAAD,EAAAO,GAAAP,EAAA,SAOTJ,KAPS,eAAAI,EAAAQ,OAAA,SAUJZ,GAVI,yBAAAI,EAAAS,SAAAd,OAAAhC,IAAA,SAAP4B,wQAAA,kBAAAA,EAAAmB,MAAAC,KAAAC,eAaGpB,6gBChBTqB,EAAA1D,EAAA,cACAA,EAAA,KACAU,EAAAV,EAAA,SACAA,EAAA,UACAA,EAAA,MACA2D,EAAA3D,EAAA,KACA4D,EAAA5D,EAAA,KACA6D,EAAA7D,EAAA,UACAA,EAAA,MACA8D,EAAA9D,EAAA,mVAeA,SAAS+D,EAAclD,GAAO,IAAAmD,EAAAR,KACpBS,EAASpD,EAAToD,KAENC,EACED,EADFC,UAAWC,EACTF,EADSE,WAAYC,EACrBH,EADqBG,SAAUC,EAC/BJ,EAD+BI,YAHPC,GAMY,EAAAZ,EAAAa,UAAS,MANrBC,EAAAC,EAAAH,EAAA,GAMrBI,EANqBF,EAAA,GAMPG,EANOH,EAAA,GAAAI,GAOwB,EAAAlB,EAAAa,WAAS,GAPjCM,EAAAJ,EAAAG,EAAA,GAOrBE,EAPqBD,EAAA,GAODE,EAPCF,EAAA,GAAAG,GAQoB,EAAAtB,EAAAa,UAAS,MAR7BU,EAAAR,EAAAO,EAAA,GAQrBE,EARqBD,EAAA,GAQHE,EARGF,EAAA,GAAAG,GASA,EAAA1B,EAAAa,cATAc,EAAAZ,EAAAW,EAAA,GASrBE,EATqBD,EAAA,GASbE,EATaF,EAAA,GAAAG,GAUQ,EAAA9B,EAAAa,WAAS,GAVjBkB,EAAAhB,EAAAe,EAAA,GAUrBE,EAVqBD,EAAA,GAUTE,EAVSF,EAAA,GAAAG,GAWM,EAAAlC,EAAAa,cAXNsB,EAAApB,EAAAmB,EAAA,GAWrBnD,EAXqBoD,EAAA,GAWVC,EAXUD,EAAA,GAYtBE,GAAsB,EAAArC,EAAAsC,SAAO,IAEnC,EAAAtC,EAAAuC,WAAU,WACR,GAAKvB,EAAL,CAKA,IAAMtC,EAAA8D,GAAA9D,EAAA+D,EAAA7D,EAAArB,QAAAsB,KAAgB,SAAAC,IAAA,IAAA4D,EAAA,OAAA9D,EAAArB,QAAA2B,KAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,cAAAF,EAAAE,KAAA,GACO,EAAAc,EAAAxB,QADP,OACd+D,EADcvD,EAAAM,KAEpB4C,EAAoBM,SAAU,EAC9BP,EAAaM,GAHO,wBAAAvD,EAAAS,SAAAd,EAAAwB,MAAhB,kBAAA5B,EAAAmB,MAAAC,KAAAC,cAMF,EAAAE,EAAA2C,eAAc5B,EAAa6B,GAAIC,sBACjCzB,GAAsB,IAEtBA,GAAsB,GAEjBgB,EAAoBM,SACvBH,UAhBFnB,GAAsB,KAkBtBL,IAMJ,IAuBMhC,EAAA+D,GAAA/D,EAAAyD,EAAA7D,EAAArB,QAAAsB,KAAc,SAAAmE,IAAA,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAA3E,EAAArB,QAAA2B,KAAA,SAAAsE,GAAA,cAAAA,EAAApE,KAAAoE,EAAAnE,MAAA,UAtBZoE,SACFC,SADED,KACFC,GAAY,EAEX1C,IACHyC,EAAeE,KAAO,yBACtBD,GAAY,IAGV1C,IAAiB,EAAAf,EAAA2C,eAAc5B,EAAa6B,GAAIC,uBAAwBtB,IAC1EiC,EAAeG,SAAW,mCAC1BF,GAAY,GAIZ7B,EADE6B,EACQD,QAKJC,EAGU,CAAAF,EAAAnE,KAAA,gBAGhB4C,GAAc,GAEVgB,OALY,EAMZC,OANY,EAOZC,OAPY,GAQX,EAAAlD,EAAA2C,eAAc5B,EAAa6B,GAAIC,wBAClCG,EAAazB,EAAiByB,WAC9BC,EAAU1B,EAAiB0B,QAC3BC,EAAiBF,EAAjB,IAA+BC,GAE3BE,GACJO,KAAM3C,EAAa6B,GACnBM,cACAF,aACAC,UACAW,aAActD,EAAKsD,aACnBnD,SAAUH,EAAKG,SACfF,UAAWD,EAAKC,UAChBC,WAAYF,EAAKE,WACjBqD,MAAOvD,EAAKuD,OAtBEN,EAAAnE,KAAA,IAyBQ,EAAAe,EAAA2D,eAAcX,GAzBtB,QAyBVC,EAzBUG,EAAA/D,KA0BR6D,EAAiCnG,EAAjCmG,UACyB,mBADdC,EAAsBpG,EAAtBoG,oBAEjBA,EAAkBD,EAAWD,GA5Bf,yBAAAG,EAAA5D,OAvBH,IACT6D,EACFC,GAqBcV,EAAA1C,MAAd,kBAAAtB,EAAAa,MAAAC,KAAAC,aAgCFiE,SACCxD,GAAcE,GAGjBsD,EAAOxD,EACHC,IACFuD,EAAUA,EAAV,IAAkBvD,GACpBuD,EAAUA,EAAV,IAAkBtD,GALlBsD,EAAO,gBAQT,IAAIC,SA4BJ,OAxBEA,EAHGjD,EAEII,EACc9D,EAAAC,QAAAC,cAAA,SAAOU,UAAU,qBAAqBgG,KAAK,OAAOC,MAAM,MAAMC,UAAA,IAGjF9G,EAAAC,QAAAC,cAACR,EAAAqH,SACCC,SAAU1C,EAAOgC,SACjB7F,OAAQC,SAAOI,OACf3B,QAASmF,EAAOgC,SAChBW,SAAUC,WAASC,MACnBC,mBAAoB,GAEpBpH,EAAAC,QAAAC,cAACmH,EAAApH,SACCwB,UAAWA,EACX6F,iBAAkBpD,EAClBqD,SAAU,SAACjB,GACT,GAAIhC,EAAOgC,SAAU,CACnB,IAAMkB,mBAAiBlD,UAChBkD,EAAUlB,SACjB/B,EAAUiD,GAEZrD,EAAoBmC,OArBPtG,EAAAC,QAAAC,cAAA,SAAOU,UAAU,qBAAqBgG,KAAK,OAAOC,MAAM,oBAAoBC,UAAA,IA2BjG9G,EAAAC,QAAAC,cAAA,OAAKU,UAAU,qBACbZ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,8CACbZ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,8BAAf,mBAEFZ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,gDACbZ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,eAAf,QACAZ,EAAAC,QAAAC,cAAA,SAAOU,UAAU,qBAAqBgG,KAAK,OAAOC,MAAA,GAAUH,EAAQI,UAAA,IACpE9G,EAAAC,QAAAC,cAAA,OAAKU,UAAU,eAAf,aACAZ,EAAAC,QAAAC,cAAA,SAAOU,UAAU,qBAAqBgG,KAAK,OAAOC,MAAOxD,EAAayD,UAAA,IACtE9G,EAAAC,QAAAC,cAAA,OAAKU,UAAU,eAAf,QACAZ,EAAAC,QAAAC,cAACR,EAAAqH,SACCC,SAAU1C,EAAO+B,KACjB5F,OAAQC,SAAOI,OACf3B,QAASmF,EAAO+B,KAChBY,SAAUC,WAASC,OAEnBnH,EAAAC,QAAAC,cAACuH,EAAAxH,SACCyH,MAAOA,UACPC,aAAc,SAACtB,GACf,GAAI/B,EAAO+B,KAAM,CACf,IAAMmB,mBAAiBlD,UAChBkD,EAAUnB,KACjB9B,EAAUiD,GAEZ7D,EAAgB0C,OAIpBrG,EAAAC,QAAAC,cAAA,OAAKU,UAAU,eAAf,aACC+F,EACD3G,EAAAC,QAAAC,cAAA,OAAKU,UAAU,uBACbZ,EAAAC,QAAAC,cAACR,EAAAkI,QACChH,UAAU,qBACViH,KAAK,eACLC,QAASrC,EACTsC,QAASrD,OAtLrB1F,EAAA,MAEA+D,EAAchC,WACZkC,KAAMjC,UAAUgH,OAChB/B,kBAAmBjF,UAAUiH,KAC7BjC,UAAWhF,UAAUiH,KAAKC,YAG5BnF,EAAc7B,cACZ+B,QACAkF,iBAAkB,gBAqLLpF,yBC3MfjE,EAAAC,QAAAC,EAAA,GAAAA,EAAA,IAKAC,MAAAH,EAAAI,EAAA,oQAA2R,2BCJ3R,IAAAC,EAAAH,EAAA,MAEA,iBAAAG,QAAAL,EAAAI,EAAAC,EAAA,MAOA,IAAAC,GAAeC,KAAA,EAEfC,eAPAA,EAQAC,gBAAAC,GAEAR,EAAA,GAAAA,CAAAG,EAAAC,GAEAD,EAAAM,SAAAX,EAAAC,QAAAI,EAAAM,4FCjBA,QAAAT,EAAA,QACAA,EAAA,KACAU,EAAAV,EAAA,KAEAA,EAAA,MACA,IAAYoJ,0JAAZpJ,EAAA,yDAEAqJ,EAAatH,WACXuH,UAAWtH,UAAUuH,OACrBC,UAAWxH,UAAUiH,KAAKC,WAC1BlC,UAAWhF,UAAUiH,KAAKC,YAG5BG,EAAanH,cACXoH,UAAWF,EAAWK,aAGxB,IAAMC,EAAqB,SAAC9B,GAC1B,OAAQA,GACN,KAAKwB,EAAWO,gBACd,MAAO,yCACT,KAAKP,EAAWQ,iBACd,MAAO,oCACT,KAAKR,EAAWS,YACd,MAAO,oCACT,QACE,MAAO,qDAIPC,EAAuB,SAAClC,GAC5B,OAAQA,GACN,KAAKwB,EAAWO,gBAChB,KAAKP,EAAWQ,iBAChB,KAAKR,EAAWS,YACd,OAAO,EACT,QACE,OAAO,IAIb,SAASR,EAAaxI,GAAO,IACnByI,EAAoCzI,EAApCyI,UAAWE,EAAyB3I,EAAzB2I,UAAWxC,EAAcnG,EAAdmG,UACxB+C,EAAUL,EAAmBJ,GAC7BU,EAAqBF,EAAqBR,GAChD,OACEtI,EAAAC,QAAAC,cAAA,OAAKU,UAAU,mBACbZ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,yBACbZ,EAAAC,QAAAC,cAAA,MAAIU,UAAU,eAAd,iBACAZ,EAAAC,QAAAC,cAAA,UAAK6I,GACJC,EAAqBhJ,EAAAC,QAAAC,cAACR,EAAAkI,QAAOhH,UAAU,2CAA2CiH,KAAK,iBAAiBC,QAAS,WAAQU,EAAUxC,MAAoB,iBAMjJqC,qFCxDf,QAAArJ,EAAA,QACAA,EAAA,KACAiK,EAAAjK,EAAA,KACAU,EAAAV,EAAA,SACAA,EAAA,QACAA,EAAA,UACAA,EAAA,WACAA,EAAA,WACAA,EAAA,0DAIA,SAASkK,EAAarJ,GAAO,IACnBsJ,EAAUtJ,EAAVsJ,MACAlG,EAAekG,EAAflG,KAAM2D,EAASuC,EAATvC,KAERX,EAAoB,SAACD,EAAWoD,GACpC,IAAMtJ,IAAYsJ,EAMlBpD,GAJEqD,UAAWzJ,UACX0J,kBAAkBxJ,EAAU,OAAS,UACrCD,OAASoD,KAAMmG,EAAetJ,cAclC,OACEE,EAAAC,QAAAC,cAAC+I,EAAAM,UAASC,MAAOC,UAAMD,OACrBxJ,EAAAC,QAAAC,cAAA,OAAKU,UAAU,iBACbZ,EAAAC,QAAAC,cAACwJ,EAAAzJ,QAAD,MACAD,EAAAC,QAAAC,cAACR,EAAAiK,YACCC,cACEP,UAAWhB,UACXiB,MAAO,gBACPzJ,OACEyI,UAAW1B,EACX4B,UAnBM,SAACxC,EAAWoD,GAM5BpD,GAJEqD,UAAWtG,UACXuG,MAAO,iBACPzJ,OAASoD,OAAMgD,0BAkBXrF,UAAU,kBACViJ,iBAAiB,MAvC3B7K,EAAA,MA8CAkK,EAAanI,WACXoI,MAAOnI,UAAUgH,QAGnBkB,EAAahI,cACXiI,oBAGaD,qFChEf,QAAAlK,EAAA,QACAA,EAAA,KACAU,EAAAV,EAAA,KACA8K,EAAA9K,EAAA,wDAEA,IAAM+K,EAAW,WAiBjB,SAASC,EAAQnK,GAAO,IAEpBe,EAUEf,EAVFe,UACAqJ,EASEpK,EATFoK,MACAC,EAQErK,EARFqK,UACA7J,EAOER,EAPFQ,KACAG,EAMEX,EANFW,SACAC,EAKEZ,EALFY,OACA0J,EAIEtK,EAJFsK,MACAC,EAGEvK,EAHFuK,QACAd,EAEEzJ,EAFFyJ,MAIF,IAFIzJ,EADFwK,OAIA,OAAQrK,EAAAC,QAAAC,cAACR,EAAA4K,MACP1J,UAAWA,EACXqJ,MAAOA,EACPC,UAAWA,EACX7J,KAAMA,EACNG,SAAUA,EACVC,OAAQA,EACR0J,MAAOA,EACPC,QAASA,EACTd,MAAOA,IAIX,OAAQjJ,GACN,KAAK0J,EACH,OAAO/J,EAAAC,QAAAC,cAAC4J,EAAAS,gBAAWJ,MAAOA,IAC5B,QACE,OAAOnK,EAAAC,QAAAC,cAAA,aAIb8J,EAAQjJ,WACNH,UAAWI,UAAUuH,OACrB0B,MAAOjJ,UAAUuH,OACjB2B,UAAWlJ,UAAUuH,OACrBlI,KAAMW,UAAUuH,OAChB/H,SAAUQ,UAAUwJ,OACpB/J,OAAQO,UAAUuH,OAClB4B,MAAOnJ,UAAUgH,OACjBoC,QAASpJ,UAAUuH,OACnBe,MAAOtI,UAAUuH,OACjB8B,OAAQrJ,UAAUC,MAGpB+I,EAAQ9I,cACNN,UAAW,GACXqJ,WAAOzK,EACP0K,eAAW1K,EACXa,KAAM,GACNG,SAAU,GACVC,OAAQ,GACR0J,OAASM,KAAM,SACfL,aAAS5K,EACT8J,MAAO,GACPe,QAAQ,aAGKL,uBCnFf,IAAA7K,EAAAH,EAAA,KAEA,iBAAAG,QAAAL,EAAAI,EAAAC,EAAA,MAOA,IAAAC,GAAeC,KAAA,EAEfC,eAPAA,EAQAC,gBAAAC,GAEAR,EAAA,GAAAA,CAAAG,EAAAC,GAEAD,EAAAM,SAAAX,EAAAC,QAAAI,EAAAM,iHCfI8F,GAAI,EACJmB,KAAM,4BAGNnB,GAAI,EACJmB,KAAM,iBAGNnB,GAAI,EACJmB,KAAM,2BAGNnB,GAAI,EACJmB,KAAM,yGCfV,IAAAgE,EAAA1L,EAAA,GAAA2L,EAAA3L,EAAA4L,EAAAF,GAAA,SAAAG,IAAmR,OAA9PA,EAAAC,OAAAC,QAAA,SAAAC,GAAgD,QAAA9L,EAAA,EAAgBA,EAAAuD,UAAAwI,OAAsB/L,IAAA,CAAO,IAAAgM,EAAAzI,UAAAvD,GAA2B,QAAAiM,KAAAD,EAA0BJ,OAAAM,UAAAC,eAAAC,KAAAJ,EAAAC,KAAyDH,EAAAG,GAAAD,EAAAC,IAAiC,OAAAH,IAAkBzI,MAAAC,KAAAC,WAInR,IAAArB,EAEAuJ,EAAAY,EAAArL,cAAA,QACAsL,EAAA,k5BAGA9J,EAEAiJ,EAAAY,EAAArL,cAAA,QACAsL,EAAA,6FAGAC,EAAA,SAAA5L,GACA,OAAA8K,EAAAY,EAAArL,cAAA,MAAA2K,GACAa,QAAA,eACG7L,GAAAuB,EAAAM,IAGHiK,EAAA,QAAA3M,EAAA4M,EAAA,+gBCrBAlJ,EAAA1D,EAAA,cACAA,EAAA,KACA6M,EAAA7M,EAAA,KACAU,EAAAV,EAAA,KACAW,EAAAX,EAAA,wDAgBA,SAAS8M,EAAiBjM,GAAO,IACvB4B,EAA0C5B,EAA1C4B,UAAW8F,EAA+B1H,EAA/B0H,SAAUD,EAAqBzH,EAArByH,iBADEhE,GAEC,EAAAZ,EAAAa,UAAS+D,GAFV9D,EAAAC,EAAAH,EAAA,GAExByI,EAFwBvI,EAAA,GAEdwI,EAFcxI,EAAA,IAI/B,EAAAd,EAAAuC,WAAU,WACJqC,GAAoByE,GAAYzE,EAAiBzB,cAAgBkG,EAASlG,aAC5EmG,EAAY1E,KACZA,IAQJ,OACEtH,EAAAC,QAAAC,cAAC2L,EAAAI,QACCC,MAAOzK,EACP0K,aATmB,SAAC7F,EAAU8F,GAAc,IACtCC,EAAgBD,EAAhBC,YACFC,EAAgBP,IAAazF,EAAW,WAAa,GAC3D,OAAQtG,EAAAC,QAAAC,cAACR,EAAA6M,UAASpB,IAAK7E,EAAST,YAAagC,KAAMvB,EAASI,KAAMoB,QAASuE,EAAazL,UAAW0L,KAOjGE,aAAc,SAAClG,GACb0F,EAAY1F,GACY,mBAAbiB,GACTA,EAASjB,IAEbmG,YAAY,GAEZzM,EAAAC,QAAAC,cAACR,EAAAkI,QAAO8E,UAAWpM,YAAUqM,WAAY9E,KAAMkE,EAAWA,EAASrF,KAAO,SAxChF1H,EAAA,KAEA8M,EAAiB/K,WACfU,UAAWT,UAAU4L,MACrBrF,SAAUvG,UAAUiH,KACpBX,iBAAkBtG,UAAUgH,QAG9B8D,EAAiB5K,cACfO,aACA8F,SAAU,KACVD,iBAAkB,gBAkCLwE,wBCpDfhN,EAAAC,QAAAC,EAAA,GAAAA,EAAA,IAKAC,MAAAH,EAAAI,EAAA,oDAA2E,+fCL3EwD,EAAA1D,EAAA,cACAA,EAAA,KACA6M,EAAA7M,EAAA,KACAU,EAAAV,EAAA,KACAW,EAAAX,EAAA,wDAgBA,SAAS6N,EAAahN,GAAO,IACnB6H,EAA0C7H,EAA1C6H,MAAOJ,EAAmCzH,EAAnCyH,iBAAkBK,EAAiB9H,EAAjB8H,aADNrE,GAEK,EAAAZ,EAAAa,UAAS+D,GAFd9D,EAAAC,EAAAH,EAAA,GAEpByI,EAFoBvI,EAAA,GAEVwI,EAFUxI,EAAA,IAI3B,EAAAd,EAAAuC,WAAU,WACJqC,GAAoBA,EAAiB/B,KAAOwG,EAASxG,IACvDyG,EAAY1E,KACZA,IAQJ,OACEtH,EAAAC,QAAAC,cAAC2L,EAAAI,QACCC,MAAOxE,EACPyE,aATe,SAAC9F,EAAM+F,GAAc,IAC9BC,EAAgBD,EAAhBC,YACFC,EAAgBP,IAAa1F,EAAO,WAAa,GACvD,OAAQrG,EAAAC,QAAAC,cAACR,EAAA6M,UAASpB,IAAK9E,EAAKd,GAAIsC,KAAMxB,EAAKK,KAAMoB,QAASuE,EAAazL,UAAW0L,KAOhFE,aAAc,SAACnG,GACb2F,EAAY3F,GACgB,mBAAjBsB,GACTA,EAAatB,IAEjBoG,YAAY,GAEZzM,EAAAC,QAAAC,cAACR,EAAAkI,QAAO8E,UAAWpM,YAAUqM,WAAY9E,KAAMkE,EAAWA,EAASrF,KAAO,iBAxChF1H,EAAA,KAEA6N,EAAa9L,WACX2G,MAAO1G,UAAU4L,MACjBtF,iBAAkBtG,UAAUgH,OAC5BL,aAAc3G,UAAUiH,MAG1B4E,EAAa3L,cACXwG,SACAJ,iBAAkB,KAClBK,aAAc,gBAkCDkF,qFCjDf,QAAA7N,EAAA,IACAiK,EAAAjK,EAAA,SACAA,EAAA,KAEA8N,EAAA9N,EAAA,wDAEA,SAAS+N,EAAelN,GAAO,IACrBmN,EAAmBnN,EAAnBmN,eACFC,GACJhD,MAAO+C,EAAeE,gBACtBC,gBAAiBH,EAAeG,gBAChCC,SAAUvN,EAAMuN,SAChBC,UAAWxN,EAAMwN,UACjBC,OAAQ,OACRC,UAAW,OACXC,OAAQ,WAGFC,EAAYT,EAAZS,QAER,OACEzN,EAAAC,QAAAC,cAAA,OAAKiK,MAAO8C,EAAa1H,GAAG,wBACzBkI,GAKPV,EAAehM,WACbiM,eAAgBhM,UAAUgH,OAAOE,WACjCkF,SAAUpM,UAAUuH,OACpB8E,UAAWrM,UAAUuH,QAGvBwE,EAAe7L,cACbkM,SAAU,QACVC,UAAW,qBAGE,EAAApE,EAAAyE,SAAQ,SAAAC,GAAA,OACrBX,eAAgBW,EAAMC,6BADT,CAEXb", "file": "3.js", "sourcesContent": ["exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".main{width:100%;height:100%;display:flex;flex-direction:column}.stack-container{flex:1 0 100%;display:flex;flex-direction:column;align-items:center;justify-content:center}.stack-container>.bp3-panel-stack-view{background:transparent}.stack-container .bp3-panel-stack-header{display:none}.stack-container>.bp3-panel-stack-appear,.stack-container>.bp3-panel-stack-enter{opacity:0}.stack-container>.bp3-panel-stack-appear-active,.stack-container>.bp3-panel-stack-enter-active,.stack-container>.bp3-panel-stack-enter-done{opacity:1;transition:opacity .2s ease-in}.stack-container>.bp3-panel-stack-exit{opacity:1}.stack-container>.bp3-panel-stack-exit-active,.stack-container>.bp3-panel-stack-exit-done{transform:unset;opacity:0;transition:opacity .2s ease-out}\", \"\"]);\n\n// exports\n", "\nvar content = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./unauthorized.scss\");\n\nif(typeof content === 'string') content = [[module.id, content, '']];\n\nvar transform;\nvar insertInto;\n\n\n\nvar options = {\"hmr\":true}\n\noptions.transform = transform\noptions.insertInto = undefined;\n\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\n\nif(content.locals) module.exports = content.locals;\n\nif(module.hot) {\n\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./unauthorized.scss\", function() {\n\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./unauthorized.scss\");\n\n\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\n\t\tvar locals = (function(a, b) {\n\t\t\tvar key, idx = 0;\n\n\t\t\tfor(key in a) {\n\t\t\t\tif(!b || a[key] !== b[key]) return false;\n\t\t\t\tidx++;\n\t\t\t}\n\n\t\t\tfor(key in b) idx--;\n\n\t\t\treturn idx === 0;\n\t\t}(content.locals, newContent.locals));\n\n\t\tif(!locals) throw new Error('Aborting CSS HMR due to changed css-modules locals.');\n\n\t\tupdate(newContent);\n\t});\n\n\tmodule.hot.dispose(function() { update(); });\n}", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".request-complete-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.request-complete-container>*{width:500px}.complete-request-header{display:flex;flex-direction:column;justify-content:start;align-items:stretch;font-size:22px}.complete-request-header.layout-item-header-2>*{text-align:left;margin-top:0;margin-bottom:0}.request-complete-border-box{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px 5px;font-size:20px}.request-complete-message{margin-top:20px}.request-complete-content{margin-top:50px;padding-left:10px;padding-right:10px;max-width:100%}\", \"\"]);\n\n// exports\n", "\nvar content = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./requestcomplete.scss\");\n\nif(typeof content === 'string') content = [[module.id, content, '']];\n\nvar transform;\nvar insertInto;\n\n\n\nvar options = {\"hmr\":true}\n\noptions.transform = transform\noptions.insertInto = undefined;\n\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\n\nif(content.locals) module.exports = content.locals;\n\nif(module.hot) {\n\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./requestcomplete.scss\", function() {\n\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./requestcomplete.scss\");\n\n\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\n\t\tvar locals = (function(a, b) {\n\t\t\tvar key, idx = 0;\n\n\t\t\tfor(key in a) {\n\t\t\t\tif(!b || a[key] !== b[key]) return false;\n\t\t\t\tidx++;\n\t\t\t}\n\n\t\t\tfor(key in b) idx--;\n\n\t\t\treturn idx === 0;\n\t\t}(content.locals, newContent.locals));\n\n\t\tif(!locals) throw new Error('Aborting CSS HMR due to changed css-modules locals.');\n\n\t\tupdate(newContent);\n\t});\n\n\tmodule.hot.dispose(function() { update(); });\n}", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { Intent } from '@blueprintjs/core';\nimport { IconNames } from '@blueprintjs/icons';\nimport AppIcon from '../AppIcon/AppIcon';\nimport './requestcomplete.scss';\n\nfunction RequestComplete(props) {\n  const { succeed } = props;\n\n  let messageComponent;\n  if (succeed) {\n    messageComponent = (\n      <React.Fragment>\n        <AppIcon icon={IconNames.TICK_CIRCLE} iconSize={90} intent={Intent.SUCCESS} />\n        <h2 className=\"request-complete-message\">Request Sent</h2>\n        <p className=\"request-complete-content\">Your access request has been sent for approval to an NARD administrator.</p>\n      </React.Fragment>\n    );\n  } else {\n    messageComponent = (\n      <React.Fragment>\n        <AppIcon icon={IconNames.ERROR} iconSize={90} intent={Intent.DANGER} />\n        <h2 className=\"request-complete-message\">Request Failed</h2>\n        <p className=\"request-complete-content\">We were unable to issue your access request.<br />Contact an administrator and try again.</p>\n      </React.Fragment>\n    );\n  }\n\n  return (\n    <div className=\"request-complete-container\">\n      <div className=\"complete-request-header layout-item-header-2\">\n        <div className=\"complete-request-header-text\">Request {succeed ? 'Sent' : 'Failed'}</div>\n      </div>\n      <div className=\"request-complete-border-box border-uline-light-medium\">\n        {messageComponent}\n      </div>\n    </div>\n  );\n}\n\nRequestComplete.propTypes = {\n  succeed: PropTypes.bool\n};\n\nRequestComplete.defaultProps = {\n  succeed: true\n};\n\nexport default RequestComplete;\n", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".request-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.request-container>*{width:500px}.access-request-header{display:flex;flex-direction:column;justify-content:start;align-items:stretch;font-size:22px}.access-request-header.layout-item-header-2>*{text-align:left;margin-top:0;margin-bottom:0}.request-border-box{display:flex;flex-direction:column;align-items:center;justify-content:center;padding:20px 5px;font-size:20px}.request-border-box>*{width:50%}.field-title:first-child{margin-top:10px}.field-title{margin-top:20px;margin-bottom:5px}.uneditable{font-size:20px;background-color:#6b7479;width:50%;padding-left:5px}.send-request-button{margin-top:30px}\", \"\"]);\n\n// exports\n", "\nvar content = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./requestaccess.scss\");\n\nif(typeof content === 'string') content = [[module.id, content, '']];\n\nvar transform;\nvar insertInto;\n\n\n\nvar options = {\"hmr\":true}\n\noptions.transform = transform\noptions.insertInto = undefined;\n\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\n\nif(content.locals) module.exports = content.locals;\n\nif(module.hot) {\n\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./requestaccess.scss\", function() {\n\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./requestaccess.scss\");\n\n\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\n\t\tvar locals = (function(a, b) {\n\t\t\tvar key, idx = 0;\n\n\t\t\tfor(key in a) {\n\t\t\t\tif(!b || a[key] !== b[key]) return false;\n\t\t\t\tidx++;\n\t\t\t}\n\n\t\t\tfor(key in b) idx--;\n\n\t\t\treturn idx === 0;\n\t\t}(content.locals, newContent.locals));\n\n\t\tif(!locals) throw new Error('Aborting CSS HMR due to changed css-modules locals.');\n\n\t\tupdate(newContent);\n\t});\n\n\tmodule.hot.dispose(function() { update(); });\n}", "import http from '../../server/Http';\nimport { UNAUTHENTICATED_SQUADRON_LIST } from '../../server/Urls';\nimport { adaptSquadron } from '../../server/longpoll/adapters/squadron';\n\nconst load = async () => {\n  let squadrons;\n  try {\n    const { json } = await http.get(UNAUTHENTICATED_SQUADRON_LIST);\n    // squadrons = json.map(adaptSquadron);\n    squadrons = json;\n  } catch (e) {\n    squadrons = [];\n  }\n\n  return squadrons;\n};\n\nexport { load };\n", "/* eslint-disable react/forbid-prop-types */\nimport React, { useEffect, useRef, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport { Button, Intent, Position, Tooltip } from '@blueprintjs/core';\nimport RoleSelector from '../Selectors/RoleSelector';\nimport SquadronSelector from '../Selectors/SquadronSelector';\nimport { roleHasAccess } from '../../authorization';\nimport { VIEW_ALL_SQUADRONS } from '../../authorization/AccessTypes';\nimport { load as loadSquadronList } from '../../services/unauthenticated/squadronList';\nimport roles from '../../utils/Roles';\nimport { requestAccess } from '../../services/user';\n\nimport './requestaccess.scss';\n\nRequestAccess.propTypes = {\n  user: PropTypes.object,\n  onRequestComplete: PropTypes.func,\n  openPanel: PropTypes.func.isRequired\n};\n\nRequestAccess.defaultProps = {\n  user: {},\n  onRequestSucceed: null\n};\n\nfunction RequestAccess(props) {\n  const { user } = props;\n  const {\n    firstName, middleName, lastName, dodIdNumber\n  } = user;\n\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [fullSquadronAccess, setFullSquadronAccess] = useState(false);\n  const [selectedSquadron, setSelectedSquadron] = useState(null);\n  const [errors, setErrors] = useState({});\n  const [requesting, setRequesting] = useState(false);\n  const [squadrons, setSquadrons] = useState([]);\n  const didLoadSquadronsRef = useRef(false);\n\n  useEffect(() => {\n    if (!selectedRole) {\n      setFullSquadronAccess(false);\n      return;\n    }\n\n    const loadSquadrons = async () => {\n      const squadronList = await loadSquadronList();\n      didLoadSquadronsRef.current = true;\n      setSquadrons(squadronList);\n    };\n\n    if (roleHasAccess(selectedRole.id, VIEW_ALL_SQUADRONS))\n      setFullSquadronAccess(true);\n    else {\n      setFullSquadronAccess(false);\n\n      if (!didLoadSquadronsRef.current)\n        loadSquadrons();\n    }\n  }, [selectedRole]);\n\n  /*\n  Validates form fields and sets errors on those fields.\n  Return true if valid/false if not.\n   */\n  const validate = () => {\n    const validateErrors = {};\n    let hasErrors = false;\n\n    if (!selectedRole) {\n      validateErrors.role = 'Select a desired role.';\n      hasErrors = true;\n    }\n\n    if (selectedRole && !roleHasAccess(selectedRole.id, VIEW_ALL_SQUADRONS) && !selectedSquadron) {\n      validateErrors.squadron = 'Select a squadron for this role.';\n      hasErrors = true;\n    }\n\n    if (hasErrors) {\n      setErrors(validateErrors);\n    } else {\n      setErrors({});\n    }\n\n    return !hasErrors;\n  };\n\n  const sendRequest = async () => {\n    const valid = validate();\n    if (valid) {\n      setRequesting(true);\n\n      let squadronId;\n      let orgCode;\n      let squadronKey;\n      if (!roleHasAccess(selectedRole.id, VIEW_ALL_SQUADRONS)) {\n        squadronId = selectedSquadron.squadronId;\n        orgCode = selectedSquadron.orgCode;\n        squadronKey = `${squadronId}-${orgCode}`;\n      }\n      const requestingUser = {\n        role: selectedRole.id,\n        squadronKey,\n        squadronId,\n        orgCode,\n        emailAddress: user.emailAddress,\n        lastName: user.lastName,\n        firstName: user.firstName,\n        middleName: user.middleName,\n        phone: user.phone\n      };\n\n      const savedUser = await requestAccess(requestingUser);\n      const { openPanel, onRequestComplete } = props;\n      if (typeof onRequestComplete === 'function')\n        onRequestComplete(openPanel, savedUser);\n    }\n  };\n\n  let name;\n  if (!firstName && !lastName)\n    name = '-- Unknown --';\n  else {\n    name = firstName;\n    if (middleName)\n      name = `${name} ${middleName}`;\n    name = `${name} ${lastName}`;\n  }\n\n  let squadronComponent;\n  if (!selectedRole)\n    squadronComponent = (<input className=\"bp3-input disabled\" type=\"text\" value=\"-- Select Role --\" readOnly />);\n  else if (fullSquadronAccess)\n    squadronComponent = (<input className=\"bp3-input disabled\" type=\"text\" value=\"All\" readOnly />);\n  else\n    squadronComponent = (\n      <Tooltip\n        isOpen={!!errors.squadron}\n        intent={Intent.DANGER}\n        content={errors.squadron}\n        position={Position.RIGHT}\n        transitionDuration={0}\n      >\n        <SquadronSelector\n          squadrons={squadrons}\n          defaultSelection={selectedSquadron}\n          onChange={(squadron) => {\n            if (errors.squadron) {\n              const newErrors = { ...errors };\n              delete newErrors.squadron;\n              setErrors(newErrors);\n            }\n            setSelectedSquadron(squadron);\n          }}\n        />\n      </Tooltip>);\n\n  return (\n    <div className=\"request-container\">\n      <div className=\"access-request-header layout-item-header-2\">\n        <div className=\"access-request-header-text\">Request Access</div>\n      </div>\n      <div className=\"request-border-box border-uline-light-medium\">\n        <div className=\"field-title\">Name</div>\n        <input className=\"bp3-input disabled\" type=\"text\" value={`${name}`} readOnly />\n        <div className=\"field-title\">ID Number</div>\n        <input className=\"bp3-input disabled\" type=\"text\" value={dodIdNumber} readOnly />\n        <div className=\"field-title\">Role</div>\n        <Tooltip\n          isOpen={!!errors.role}\n          intent={Intent.DANGER}\n          content={errors.role}\n          position={Position.RIGHT}\n        >\n          <RoleSelector\n            roles={roles}\n            onRoleChange={(role) => {\n            if (errors.role) {\n              const newErrors = { ...errors };\n              delete newErrors.role;\n              setErrors(newErrors);\n            }\n            setSelectedRole(role);\n          }}\n          />\n        </Tooltip>\n        <div className=\"field-title\">Squadrons</div>\n        {squadronComponent}\n        <div className=\"send-request-button\">\n          <Button\n            className=\"bp3-intent-primary\"\n            text=\"Send Request\"\n            onClick={sendRequest}\n            loading={requesting}\n          />\n        </div>\n\n      </div>\n    </div>\n  );\n}\n\nexport default RequestAccess;\n", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".error-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.error-title{font-size:6em;color:red;text-shadow:-1px 1px 0 #fff,1px 1px 0 #fff,1px -1px 0 #fff,-1px -1px 0 #fff}.request-access-button{margin-top:2em}\", \"\"]);\n\n// exports\n", "\nvar content = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./accessdenied.scss\");\n\nif(typeof content === 'string') content = [[module.id, content, '']];\n\nvar transform;\nvar insertInto;\n\n\n\nvar options = {\"hmr\":true}\n\noptions.transform = transform\noptions.insertInto = undefined;\n\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\n\nif(content.locals) module.exports = content.locals;\n\nif(module.hot) {\n\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./accessdenied.scss\", function() {\n\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./accessdenied.scss\");\n\n\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\n\t\tvar locals = (function(a, b) {\n\t\t\tvar key, idx = 0;\n\n\t\t\tfor(key in a) {\n\t\t\t\tif(!b || a[key] !== b[key]) return false;\n\t\t\t\tidx++;\n\t\t\t}\n\n\t\t\tfor(key in b) idx--;\n\n\t\t\treturn idx === 0;\n\t\t}(content.locals, newContent.locals));\n\n\t\tif(!locals) throw new Error('Aborting CSS HMR due to changed css-modules locals.');\n\n\t\tupdate(newContent);\n\t});\n\n\tmodule.hot.dispose(function() { update(); });\n}", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { Button } from '@blueprintjs/core';\n\nimport './accessdenied.scss';\nimport * as ErrorTypes from '../../utils/UserErrorTypes';\n\nAccessDenied.propTypes = {\n  errorType: PropTypes.string,\n  onRequest: PropTypes.func.isRequired,\n  openPanel: PropTypes.func.isRequired\n};\n\nAccessDenied.defaultProps = {\n  errorType: ErrorTypes.UNKNOWN_CAC\n};\n\nconst getMessageFromType = (type) => {\n  switch (type) {\n    case ErrorTypes.APPROVAL_DENIED:\n      return 'Your approval request has been denied.';\n    case ErrorTypes.PENDING_APPROVAL:\n      return 'Your approval request is pending.';\n    case ErrorTypes.CAC_REVOKED:\n      return 'Your CAC access has been revoked.';\n    default:\n      return 'You do not have authorization to view this page.';\n  }\n};\n\nconst doesErrorAllowAccess = (type) => {\n  switch (type) {\n    case ErrorTypes.APPROVAL_DENIED:\n    case ErrorTypes.PENDING_APPROVAL:\n    case ErrorTypes.CAC_REVOKED:\n      return false;\n    default:\n      return true;\n  }\n};\n\nfunction AccessDenied(props) {\n  const { errorType, onRequest, openPanel } = props;\n  const message = getMessageFromType(errorType);\n  const allowAccessRequest = doesErrorAllowAccess(errorType);\n  return (\n    <div className=\"error-container\">\n      <div className=\"error-content-wrapper\">\n        <h1 className=\"error-title\">Access Denied</h1>\n        <h2>{message}</h2>\n        {allowAccessRequest ? <Button className=\"request-access-button bp3-intent-primary\" text=\"Request Access\" onClick={() => { onRequest(openPanel); }} /> : null}\n      </div>\n    </div>\n  );\n}\n\nexport default AccessDenied;\n", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { Provider } from 'react-redux';\nimport { PanelStack } from '@blueprintjs/core';\nimport State from 'holodeck-ui/src/State';\nimport Classification from '../layout/Classification';\nimport AccessDenied from './AccessDenied';\nimport RequestAccess from './RequestAccess';\nimport RequestComplete from './RequestComplete';\n\nimport './unauthorized.scss';\n\nfunction Unauthorized(props) {\n  const { error } = props;\n  const { user, type } = error;\n\n  const onRequestComplete = (openPanel, requestedUser) => {\n    const succeed = !!requestedUser;\n    const completePanel = {\n      component: RequestComplete,\n      title: `Request ${succeed ? 'Sent' : 'Failed'}`,\n      props: { user: requestedUser, succeed }\n    };\n    openPanel(completePanel);\n  };\n\n  const onRequest = (openPanel, requestedUser) => {\n    const requestPanel = {\n      component: RequestAccess,\n      title: 'Request Access',\n      props: { user, onRequestComplete }\n    };\n    openPanel(requestPanel);\n  };\n\n  return (\n    <Provider store={State.store}>\n      <div className=\"main bp3-dark\">\n        <Classification />\n        <PanelStack\n          initialPanel={{\n            component: AccessDenied,\n            title: 'Access Denied',\n            props: {\n              errorType: type,\n              onRequest\n            }\n          }}\n          className=\"stack-container\"\n          showPanelHeader={false}\n        />\n      </div>\n    </Provider>\n  );\n}\n\nUnauthorized.propTypes = {\n  error: PropTypes.object\n};\n\nUnauthorized.defaultProps = {\n  error: {}\n};\n\nexport default Unauthorized;\n", "import React from 'react';\nimport PropTypes from 'prop-types';\nimport { Icon } from '@blueprintjs/core';\nimport { ReactComponent as FCF_SHIELD } from '../../images/icon_svgs/fcf_shield.svg';\n\nconst FCF_ICON = 'fcf_icon';\n\n/**\n * Component to display icons in the app.\n * Icons are either from blueprint or svgs that have been imported\n * @param props - blueprintjs props plus custom boolean to notify component\n * whether you want blueprint icon or svg (link to blueprint doc: https://blueprintjs.com/docs/#core/components/icon)\n * @returns AppIcon element - either blueprint or custom\n *\n * How to add a svg:\n * 1. Import svgs in src/images/svg_icons\n * 2. import { ReactComponent as SVG } from '../../images/icon_svgs/SVG_NAME.svg';\n * 3. Create a const. For example, const SVG_NAME = 'svg_name'. The value of the const is\n *    expected value for the icon prop when custom = true\n * 4. Add const to switch statement below that returns desire svg react component\n */\n\nfunction AppIcon(props) {\n  const {\n    className,\n    color,\n    htmltitle,\n    icon,\n    iconSize,\n    intent,\n    style,\n    tagName,\n    title,\n    custom\n  } = props;\n\n  if (!custom) {\n    return (<Icon\n      className={className}\n      color={color}\n      htmltitle={htmltitle}\n      icon={icon}\n      iconSize={iconSize}\n      intent={intent}\n      style={style}\n      tagName={tagName}\n      title={title}\n    />);\n  }\n\n  switch (icon) {\n    case FCF_ICON:\n      return <FCF_SHIELD style={style} />;\n    default:\n      return <div />;\n  }\n}\n\nAppIcon.propTypes = {\n  className: PropTypes.string,\n  color: PropTypes.string,\n  htmltitle: PropTypes.string,\n  icon: PropTypes.string,\n  iconSize: PropTypes.number,\n  intent: PropTypes.string,\n  style: PropTypes.object,\n  tagName: PropTypes.string,\n  title: PropTypes.string,\n  custom: PropTypes.bool\n};\n\nAppIcon.defaultProps = {\n  className: '',\n  color: undefined,\n  htmltitle: undefined,\n  icon: '',\n  iconSize: 16, // blueprint default\n  intent: '',\n  style: { fill: 'white' },\n  tagName: undefined,\n  title: '',\n  custom: false\n};\n\nexport default AppIcon;\n", "\nvar content = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./selectors.scss\");\n\nif(typeof content === 'string') content = [[module.id, content, '']];\n\nvar transform;\nvar insertInto;\n\n\n\nvar options = {\"hmr\":true}\n\noptions.transform = transform\noptions.insertInto = undefined;\n\nvar update = require(\"!../../../node_modules/style-loader/lib/addStyles.js\")(content, options);\n\nif(content.locals) module.exports = content.locals;\n\nif(module.hot) {\n\tmodule.hot.accept(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./selectors.scss\", function() {\n\t\tvar newContent = require(\"!!../../../node_modules/css-loader/index.js??ref--9-1!../../../node_modules/sass-loader/lib/loader.js!./selectors.scss\");\n\n\t\tif(typeof newContent === 'string') newContent = [[module.id, newContent, '']];\n\n\t\tvar locals = (function(a, b) {\n\t\t\tvar key, idx = 0;\n\n\t\t\tfor(key in a) {\n\t\t\t\tif(!b || a[key] !== b[key]) return false;\n\t\t\t\tidx++;\n\t\t\t}\n\n\t\t\tfor(key in b) idx--;\n\n\t\t\treturn idx === 0;\n\t\t}(content.locals, newContent.locals));\n\n\t\tif(!locals) throw new Error('Aborting CSS HMR due to changed css-modules locals.');\n\n\t\tupdate(newContent);\n\t});\n\n\tmodule.hot.dispose(function() { update(); });\n}", "const roles = [\n  {\n    id: 1,\n    name: 'Dashboard Administrator'\n  },\n  {\n    id: 2,\n    name: 'Command User'\n  },\n  {\n    id: 3,\n    name: 'Squadron Administrator'\n  },\n  {\n    id: 4,\n    name: 'Squadron User'\n  }\n];\n\nexport default [...roles];\n", "function _extends() { _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\n\nimport React from \"react\";\n\nvar _ref =\n/*#__PURE__*/\nReact.createElement(\"path\", {\n  d: \"M420.498 65.192c-48.503-8.692-93.169-35.18-115.477-50.195C290.446 5.186 273.495 0 255.999 0s-34.447 5.186-49.021 14.997C184.67 30.012 140.004 56.501 91.502 65.193 62.776 70.34 41.926 95.195 41.926 124.29v120.709c0 39.877 11.156 78.75 33.159 115.539 17.213 28.781 41.064 56.288 70.888 81.757 50.147 42.825 99.803 65.157 101.892 66.086l8.134 3.619 8.136-3.619c2.089-.929 51.745-23.261 101.892-66.086 29.823-25.469 53.674-52.976 70.888-81.757 22.003-36.79 33.159-75.662 33.159-115.539v-120.71c0-29.095-20.85-53.95-49.576-59.097zm9.56 179.806c0 59.45-30.033 115.375-89.267 166.224-34.432 29.558-69.39 48.824-84.791 56.643-35.401-17.987-174.061-96.536-174.061-222.866v-120.71c0-9.695 6.99-17.985 16.621-19.711 55.718-9.985 105.843-39.615 130.761-56.387 7.947-5.349 17.173-8.177 26.678-8.177 9.505 0 18.73 2.828 26.677 8.178 24.919 16.772 75.043 46.402 130.761 56.388 9.63 1.726 16.621 10.015 16.621 19.711v120.707z\"\n});\n\nvar _ref2 =\n/*#__PURE__*/\nReact.createElement(\"path\", {\n  d: \"M225.014 294.431l-68.38-64.018-27.348 29.21 98.307 92.036 156.376-175.354-29.864-26.632z\"\n});\n\nvar SvgFcfShield = function SvgFcfShield(props) {\n  return React.createElement(\"svg\", _extends({\n    viewBox: \"0 0 512 512\"\n  }, props), _ref, _ref2);\n};\n\nexport default __webpack_public_path__ + \"fonts/5360b6db.svg\";\nexport { SvgFcfShield as ReactComponent };", "/* eslint-disable react/forbid-prop-types */\nimport React, { useEffect, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport { Select } from '@blueprintjs/select';\nimport { Button, MenuItem } from '@blueprintjs/core';\nimport { IconNames } from '@blueprintjs/icons';\n\nimport './selectors.scss';\n\nSquadronSelector.propTypes = {\n  squadrons: PropTypes.array,\n  onChange: PropTypes.func,\n  defaultSelection: PropTypes.object\n};\n\nSquadronSelector.defaultProps = {\n  squadrons: [],\n  onChange: null,\n  defaultSelection: null\n};\n\nfunction SquadronSelector(props) {\n  const { squadrons, onChange, defaultSelection } = props;\n  const [selected, setSelected] = useState(defaultSelection);\n\n  useEffect(() => {\n    if (defaultSelection && selected && defaultSelection.squadronKey !== selected.squadronKey)\n      setSelected(defaultSelection);\n  }, [defaultSelection]);\n\n  const renderSquadron = (squadron, itemProps) => {\n    const { handleClick } = itemProps;\n    const selectedClass = selected === squadron ? 'selected' : '';\n    return (<MenuItem key={squadron.squadronKey} text={squadron.name} onClick={handleClick} className={selectedClass} />);\n  };\n\n  return (\n    <Select\n      items={squadrons}\n      itemRenderer={renderSquadron}\n      onItemSelect={(squadron) => {\n        setSelected(squadron);\n        if (typeof onChange === 'function')\n          onChange(squadron);\n      }}\n      filterable={false}\n    >\n      <Button rightIcon={IconNames.CARET_DOWN} text={selected ? selected.name : '---'} />\n    </Select>\n  );\n}\n\nexport default SquadronSelector;\n", "exports = module.exports = require(\"../../../node_modules/css-loader/lib/css-base.js\")(false);\n// imports\n\n\n// module\nexports.push([module.id, \".selected{background-color:rgba(138,155,168,.15)}\", \"\"]);\n\n// exports\n", "import React, { useEffect, useState } from 'react';\nimport PropTypes from 'prop-types';\nimport { Select } from '@blueprintjs/select';\nimport { Button, MenuItem } from '@blueprintjs/core';\nimport { IconNames } from '@blueprintjs/icons';\n\nimport './selectors.scss';\n\nRoleSelector.propTypes = {\n  roles: PropTypes.array,\n  defaultSelection: PropTypes.object,\n  onRoleChange: PropTypes.func\n};\n\nRoleSelector.defaultProps = {\n  roles: [],\n  defaultSelection: null,\n  onRoleChange: null\n};\n\nfunction RoleSelector(props) {\n  const { roles, defaultSelection, onRoleChange } = props;\n  const [selected, setSelected] = useState(defaultSelection);\n\n  useEffect(() => {\n    if (defaultSelection && defaultSelection.id !== selected.id)\n      setSelected(defaultSelection);\n  }, [defaultSelection]);\n\n  const renderRole = (role, itemProps) => {\n    const { handleClick } = itemProps;\n    const selectedClass = selected === role ? 'selected' : '';\n    return (<MenuItem key={role.id} text={role.name} onClick={handleClick} className={selectedClass} />);\n  };\n\n  return (\n    <Select\n      items={roles}\n      itemRenderer={renderRole}\n      onItemSelect={(role) => {\n        setSelected(role);\n        if (typeof onRoleChange === 'function')\n          onRoleChange(role);\n      }}\n      filterable={false}\n    >\n      <Button rightIcon={IconNames.CARET_DOWN} text={selected ? selected.name : 'Select Role'} />\n    </Select>\n  );\n}\n\nexport default RoleSelector;\n", "/* eslint-disable linebreak-style */\n/* eslint-disable react/forbid-prop-types */\nimport React from 'react';\nimport { connect } from 'react-redux';\nimport PropTypes from 'prop-types';\n\nimport { classificationSliceName } from '../../../store/classification';\n\nfunction Classification(props) {\n  const { classification } = props;\n  const bannerStyle = {\n    color: classification.foregroundColor,\n    backgroundColor: classification.backgroundColor,\n    fontSize: props.fontSize,\n    textAlign: props.textAlign,\n    height: '25px',\n    minHeight: '25px', // ie fix\n    cursor: 'default'\n  };\n\n  const { marking } = classification;\n\n  return (\n    <div style={bannerStyle} id=\"classificationBanner\">\n      {marking}\n    </div>\n  );\n}\n\nClassification.propTypes = {\n  classification: PropTypes.object.isRequired,\n  fontSize: PropTypes.string,\n  textAlign: PropTypes.string\n};\n\nClassification.defaultProps = {\n  fontSize: '1.3em',\n  textAlign: 'center'\n};\n\nexport default connect(state => ({\n  classification: state[classificationSliceName]\n}))(Classification);\n"], "sourceRoot": ""}