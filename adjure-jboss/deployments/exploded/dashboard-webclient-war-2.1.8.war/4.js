(window.webpackJsonp=window.webpackJsonp||[]).push([[4],{1613:function(e,t,r){(e.exports=r(23)(!1)).push([e.i,".error-container{display:flex;flex-direction:column;align-items:center;justify-content:center;height:100%}.error-msg{font-size:1.5em}",""])},1614:function(e,t,r){var n=r(1613);"string"==typeof n&&(n=[[e.i,n,""]]);var o={hmr:!0,transform:void 0,insertInto:void 0};r(28)(n,o);n.locals&&(e.exports=n.locals)},833:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=a(r(5)),o=a(r(18));function a(e){return e&&e.__esModule?e:{default:e}}function s(e){var t=e.message;return n.default.createElement("div",{className:"error-container"},n.default.createElement("h1",null,"Unable to load NARD Dashboard"),n.default.createElement("p",{className:"error-msg"},t))}r(1614),s.propTypes={message:o.default.string.isRequired},t.default=s}}]);
//# sourceMappingURL=4.js.map