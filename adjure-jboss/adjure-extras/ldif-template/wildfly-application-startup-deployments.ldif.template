
#
# default Startup Config for all standard (non-exploded) deployments
# increase defaults for initial status interval and periodic status interval because
# the jboss-cli calls that they turn into take some time and I don't want to spam the system if we
# have a bunch of deployments. 
#
dn: uniqueIdentifier=%%UUID:wildflyServerStartupConfigForDeployments%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {serviceScriptPath}/h/adjure-jboss/adjure-extras/private-tools/deploy_standard_ctrl
configVar: {workingDirectory}/h/adjure-jboss/adjure-extras/private-tools
# TODO: Should claim this as a failed status return code, but we'll let the timeout deal with it
# because sometimes we get a "failure" before the success but after we deploy, even though it should
# just return 1 to indicate it doesn't know the result yet.
# Since we're doing this, we'll lower the timeout time to a more reasonable (but still large) 3min
#configVar: {failedStatusReturnCode}2
# Note: these status intervals are VERY LOW if you switch to the jboss cli, but the default
# deployment scripts (deploy_standard_ctrl and deploy_exploded_ctrl) work well with them.
configVar: {initialStatusInterval}3
configVar: {periodicStatusInterval}20
configVar: {timeoutTime}180
configVar: {unknownStatusReturnCode}1
cn: default startup config for wildfly standard deployments

#
# default Startup Config for all exploded deployments
# increase defaults for initial status interval and periodic status interval because
# the jboss-cli calls that they turn into take some time and I don't want to spam the system if we
# have a bunch of deployments.
#
dn: uniqueIdentifier=%%UUID:wildflyServerStartupConfigForExplodedDeployments%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {serviceScriptPath}/h/adjure-jboss/adjure-extras/private-tools/deploy_exploded_ctrl
configVar: {workingDirectory}/h/adjure-jboss/adjure-extras/private-tools
# TODO: Should claim this as a failed status return code, but we'll let the timeout deal with it
# because sometimes we get a "failure" before the success but after we deploy, even though it should
# just return 1 to indicate it doesn't know the result yet.
# Since we're doing this, we'll lower the timeout time to a more reasonable (but still large) 3min
#configVar: {failedStatusReturnCode}2
# Note: these status intervals are VERY LOW if you switch to the jboss cli, but the default
# deployment scripts (deploy_standard_ctrl and deploy_exploded_ctrl) work well with them.
configVar: {initialStatusInterval}3
configVar: {periodicStatusInterval}20
configVar: {timeoutTime}180
configVar: {unknownStatusReturnCode}1
cn: default startup config for wildfly exploded deployments
