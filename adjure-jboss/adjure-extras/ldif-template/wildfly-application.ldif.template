
#------------------------------------------------------------------------------------------------------------------------------------------
# You can use this file as a template for creating the LDAP ldif entries that define the application and application installation. These
# entries reference application configuration entries, startup configuration, bus service entries, etc.
#------------------------------------------------------------------------------------------------------------------------------------------
#
# __applicationIdParamPrefix__ - replaced with the name you use in your other application ldif templates to refer to UUID values for your
#                                application. For example, if your application is the session server, you may use sessionServerInstallation,
#                                sessionServerConfig, sessionServerApplication, sessionServerStartup, etc. With this example, you would use
#                                sessionServer as the value for __applicationIdParamPrefix__
# __applicationLongName__ - the logical name of the application useful in comments and descriptions, but not generally for lookup. In the
#                           above example, you might use 'Session Server' as the value for this
# __applicationSimpleName__ - the simple logical name of the application. This is used in situations where no spaces are allowed, though
#                             the use of upper case, lower case, and numeric is fine. In the above example, you might use 'SessionServer'
#                             as the value for this
# __applicationMajorVersion__ - the major version for your application
# __applicationMinorVersion__ - the minor version for your application
# __applicationPatchVersion__ - the patch version for your application

######################################################
# Adjure Wildfly (21.0.2) Server
######################################################
#
# Adjure Wildfly (21.0.2) Server - Application
#
dn: uniqueIdentifier=%%UUID:wildflyServerApplication%%,ou=Application,dc=ticom-geo,dc=com
cn: Wildfly (21.0.2) Server Application
objectClass: tgiApplicationObject
applicationName: wildflyServer
majorVersion: 1
minorVersion: 0
patchVersion: 0

#
# Adjure Wildfly (21.0.2) Server - Application Installation
#
dn: uniqueIdentifier=%%UUID:wildflyServerInstallation%%,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Wildfly (21.0.2) Server
objectClass: tgiApplicationInstallationObject
hostRefId: uniqueIdentifier=%%UUID:host%%,ou=Host,dc=ticom-geo,dc=com
startupRefId: uniqueIdentifier=%%UUID:wildflyServerStartup%%,ou=StartupConfig,dc=ticom-geo,dc=com

