#!/bin/bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

source /h/adjure/base/config/adjure_env.sh
source ${ADJURE_SCRIPTING_COMMON}

function help {
  echo "set-shiro-mode.sh [--auto|--pkitest|--pki|--casport|--force-pki]"
}

if [[ -z "$1" ]]; then
  if [ -z "${WILDFLY_SHIRO_CURRENT_MODE}" ]; then
    use_ssl=$(get_adjure_base_config WILDFLY_USE_SSL)
    if [ "$use_ssl" == "y" ]; then
      use_casport=$(get_adjure_base_config WILDFLY_USE_CASPORT)
      if [ "$use_casport" == "y" ]; then
        MODE="--casport"
      else
        MODE="--pki"
      fi
    else
      MODE="--auto"
    fi
  else
    MODE="${WILDFLY_SHIRO_CURRENT_MODE}"
  fi
else
  if [[ "$1" != "--auto" ]] && [[ "$1" != "--pkitest" ]] && [[ "$1" != "--pki" ]] && [[ "$1" != "--casport" ]] && [[ "$1" != "--force-pki" ]]; then
      help
      exit 1
  fi
  MODE=$1
  if [[ "$1" == "--force-pki" ]]; then
    if [[ -n "${WILDFLY_SHIRO_LAST_PKI_MODE}" ]]; then
      MODE=${WILDFLY_SHIRO_LAST_PKI_MODE}
    else
      # If mode has only ever been set to '--auto', then set the mode to '--pki'
      MODE="--pki"
    fi
  fi
fi

if [[ -z "$WILDFLY_SHIRO_MODE_ALLOW_AUTO" ]]; then
  allow_auto="y"
else
  allow_auto=${WILDFLY_SHIRO_MODE_ALLOW_AUTO}
fi
if [[ -z "$WILDFLY_SHIRO_MODE_ALLOW_PKI" ]]; then
  allow_pki="y"
else
  allow_pki=${WILDFLY_SHIRO_MODE_ALLOW_PKI}
fi
if [[ -z "$WILDFLY_SHIRO_MODE_ALLOW_PKITEST" ]]; then
  allow_pkitest="y"
else
  allow_pkitest=${WILDFLY_SHIRO_MODE_ALLOW_PKITEST}
fi
if [[ -z "$WILDFLY_SHIRO_MODE_ALLOW_CASPORT" ]]; then
  allow_casport="y"
else
  allow_casport=${WILDFLY_SHIRO_MODE_ALLOW_CASPORT}
fi

allow_auto=$(echo ${allow_auto} | tr '[:upper:]' '[:lower:]')
allow_pki=$(echo ${allow_pki} | tr '[:upper:]' '[:lower:]')
allow_pkitest=$(echo ${allow_pkitest} | tr '[:upper:]' '[:lower:]')
allow_casport=$(echo ${allow_casport} | tr '[:upper:]' '[:lower:]')
if [[ "${MODE}" == "--auto" ]]; then
  if [[ "${allow_auto}" != "y" ]]; then
    echo "System configuration does not allow 'auto' Shiro configuration"
    exit 2
  fi
else
  if [[ "${allow_pki}" != "y" ]]; then
    echo "System configuration does not allow 'pki', 'pkitest' or 'casport' Shiro configuration"
    exit 2
  fi
  if [[ "${MODE}" == "--casport" ]]; then
    if [[ "${allow_casport}" != "y" ]]; then
      echo "System configuration does not allow 'casport' Shiro configuration"
      exit 2
    fi
  else
    if [[ "${MODE}" == "--pkitest" ]]; then
      if [[ "${allow_pkitest}" != "y" ]]; then
        echo "System configuration does not allow 'pkitest' Shiro configuration"
        exit 2
      fi
    fi
  fi
fi

function set_shiro_pki {
  if [[ ! -f $1.orig ]]; then
    cp -pf $1 $1.orig
  fi
  cp -f $1 $1.current
  ${SCRIPT_DIR}/set-shiro-mode-single-file.pl $1.current $1 --pki
  rm -f $1.current
}

function set_shiro_pkitest {
  if [[ ! -f $1.orig ]]; then
    cp -pf $1 $1.orig
  fi
  cp -f $1 $1.current
  ${SCRIPT_DIR}/set-shiro-mode-single-file.pl $1.current $1 --pkitest
  rm -f $1.current
}

function set_shiro_casport {
  if [[ ! -f $1.orig ]]; then
    cp -pf $1 $1.orig
  fi
  cp -f $1 $1.current
  ${SCRIPT_DIR}/set-shiro-mode-single-file.pl $1.current $1 --casport
  rm -f $1.current
}

function set_shiro_auto {
  if [[ ! -f $1.orig ]]; then
    cp -pf $1 $1.orig
  fi
  cp -f $1 $1.current
  ${SCRIPT_DIR}/set-shiro-mode-single-file.pl $1.current $1 --auto
  rm -f $1.current
}

if [[ "${MODE}" == "--auto" ]]; then
  # If current mode is some PKI mode, then save it as last PKI mode
  echo "Setting Shiro configuration mode to: auto"
  if [[ "${WILDFLY_SHIRO_CURRENT_MODE}" != "--auto" ]] && [[ -n "${WILDFLY_SHIRO_CURRENT_MODE}" ]]; then
    $(set_adjure_base_config WILDFLY_SHIRO_LAST_PKI_MODE "${WILDFLY_SHIRO_CURRENT_MODE}")
  fi
  $(set_adjure_base_config WILDFLY_USE_SSL "n")
  $(set_adjure_base_config WILDFLY_USE_CASPORT "n")
else
  $(set_adjure_base_config WILDFLY_SHIRO_LAST_PKI_MODE "${MODE}")
  if [[ "${MODE}" == "--casport" ]]; then
    echo "Setting Shiro configuration mode to: pki + casport"
    $(set_adjure_base_config WILDFLY_USE_CASPORT "y")
  else
    if [[ "${MODE}" == "--pkitest" ]]; then
      echo "Setting Shiro configuration mode to: pkitest"
    else
      echo "Setting Shiro configuration mode to: pki"
    fi
    $(set_adjure_base_config WILDFLY_USE_CASPORT "n")
  fi
  $(set_adjure_base_config WILDFLY_USE_SSL "y")
fi
# Save current mode
$(set_adjure_base_config WILDFLY_SHIRO_CURRENT_MODE "${MODE}")

MODULES_BASE=${ADJURE_WILDFLY_MODULES_PATH}/com/ticomgeo/
for f in `ls ${MODULES_BASE}/*/configuration/main/properties/*shiro.ini`; do
  if [[ "${MODE}" == "--auto" ]]; then
    set_shiro_auto $f
  else
    if [[ "${MODE}" == "--pkitest" ]]; then
      set_shiro_pkitest $f
    else
        if [[ "${MODE}" == "--pki" ]]; then
          set_shiro_pki $f
        else
          set_shiro_casport $f
        fi
    fi
  fi
done
