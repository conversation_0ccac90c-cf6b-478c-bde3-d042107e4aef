#!/usr/bin/env bash

#!/bin/bash

source /h/adjure/base/config/adjure_env.sh

if [ -n "${INSTALL_PRESERVE_DIR}" ] ; then
    echo "Backing up Wildfly configuration to ${INSTALL_PRESERVE_DIR}"
    if [[ -z "$ADJURE_WILDFLY_SECMGR" ]]; then
        ADJURE_WILDFLY_SECMGR="false"
    fi
    echo "ADJURE_WILDFLY_SECMGR=$ADJURE_WILDFLY_SECMGR" > $INSTALL_PRESERVE_DIR/wildfly.properties
    echo "ADJURE_WILDFLY_PRES_MEM=$ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG" >> $INSTALL_PRESERVE_DIR/wildfly.properties
    echo "ADJURE_WILDFLY_PRES_HTTP_PORT=$WILDFLY_HTTP_PORT" >> $INSTALL_PRESERVE_DIR/wildfly.properties
    echo "ADJURE_WILDFLY_PRES_HTTPS_PORT=$WILDFLY_HTTPS_PORT" >> $INSTALL_PRESERVE_DIR/wildfly.properties
    echo "ADJURE_WILDFLY_SHIRO_CURRENT_MODE=$WILDFLY_SHIRO_CURRENT_MODE" >> $INSTALL_PRESERVE_DIR/wildfly.properties
    echo "ADJURE_WILDFLY_USE_SSL=$WILDFLY_USE_SSL" >> $INSTALL_PRESERVE_DIR/wildfly.properties
    echo "ADJURE_WILDFLY_USE_CASPORT=$WILDFLY_USE_CASPORT" >> $INSTALL_PRESERVE_DIR/wildfly.properties
fi