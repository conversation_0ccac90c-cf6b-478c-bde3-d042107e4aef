#!/bin/bash

if [[ -z "$1" ]]; then
  echo "Must provide directory to add"
  exit 1
fi

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_SCRIPTING_COMMON

{
  flock -x 200

  extradirs=$(get_adjure_base_config ADJURE_WILDFLY_EXTRA_NATIVE_LIBS_DIRS '')
  if [[ -z "$extradirs" ]]; then
    extradirs=$1
  else
    extradirs=$extradirs:$1
  fi

  set_adjure_base_config ADJURE_WILDFLY_EXTRA_NATIVE_LIBS_DIRS $extradirs

} 200>/tmp/adjure_wildfly_add_native_libs_dir.sh.lock

rm -f /tmp/adjure_wildfly_add_native_libs_dir.sh.lock
