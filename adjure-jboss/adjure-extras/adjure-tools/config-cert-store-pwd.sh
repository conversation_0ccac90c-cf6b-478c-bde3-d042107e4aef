#!/bin/bash
source /h/adjure/base/config/adjure_env.sh
source $ADJURE_SCRIPTING_COMMON

# error if not run as root or opsys
username=$(whoami)
if [ "$username" != "root" ] && [ "$username" != "opsys" ] ; then
    echo "You may only run this command as root or opsys. (You are $username.)"
    exit 2
fi

script_path=$(dirname $(readlink -f $0))

pwd_is_good=0
while [ "$pwd_is_good" == "0" ] ; do
  read -p "Keystore file password: " ks_password
  if [ -n "$ks_password" ]; then
      pwd_is_good="1"
  fi
done

pwd_is_good=0
while [ "$pwd_is_good" == "0" ] ; do
  read -p "Truststore file password: " ts_password
  if [ -n "$ts_password" ]; then
      pwd_is_good="1"
  fi
done

set_adjure_base_config WILDFLY_KEYSTORE_PWD "$ks_password"
set_adjure_base_config WILDFLY_TRUSTSTORE_PWD "$ts_password"

#
# Run all the extension scripts
#
script_path=$(dirname $(readlink -f $0))
if [ -e "${script_path}/cert-ext" ];
then
  for f in $(find "${script_path}/cert-ext" -maxdepth 1 -type f); do
    #echo "Running cert store password change extension script: " $f
    . $f
  done
fi
