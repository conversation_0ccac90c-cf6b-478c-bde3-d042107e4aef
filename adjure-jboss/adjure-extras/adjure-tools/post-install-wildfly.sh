#!/bin/bash

SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_WILDFLY_TOOLS

max_mem_is_good=0
if is_upgrade ; then
    source /etc/sysconfig/adjure_preserve
    if [ -f "${INSTALL_PRESERVE_DIR}/wildfly.properties" ] ; then
        source ${INSTALL_PRESERVE_DIR}/wildfly.properties
        if [[ -z "$ADJURE_WILDFLY_SECMGR" ]]; then
            ADJURE_WILDFLY_SECMGR="false"
        fi
        WILDFLY_SECMGR="$ADJURE_WILDFLY_SECMGR"
        echo "Restoring memory configuration for Wildfly: $ADJURE_WILDFLY_PRES_MEM MB"
        max_mem=$ADJURE_WILDFLY_PRES_MEM
        max_mem_is_good=$(expr "$max_mem" : "^[0-9][0-9]*$")
        WILDFLY_HTTP_PORT=$ADJURE_WILDFLY_PRES_HTTP_PORT
        WILDFLY_HTTPS_PORT=$ADJURE_WILDFLY_PRES_HTTPS_PORT
        shiro_current_mode=$ADJURE_WILDFLY_SHIRO_CURRENT_MODE
        WILDFLY_USE_SSL=$ADJURE_WILDFLY_USE_SSL
        WILDFLY_USE_CASPORT=$ADJURE_WILDFLY_USE_CASPORT
    fi
fi
while [ "$max_mem_is_good" == "0" ] ; do
    read -p "Max memory (in MB) for Wildfly to consume? [$ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG]: " max_mem
    if [ "$max_mem" == "" ]; then
        max_mem=$ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG
    fi
    max_mem_is_good=$(expr "$max_mem" : "^[0-9][0-9]*$")
done

set_adjure_base_config ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG $max_mem

if [[ -z "$WILDFLY_HTTP_PORT" ]]; then
    suggest_http_port="8080"
else
    suggest_http_port=$WILDFLY_HTTP_PORT
fi

if [[ -z "$WILDFLY_HTTPS_PORT" ]]; then
    suggest_https_port="8443"
else
    suggest_https_port=$WILDFLY_HTTPS_PORT
fi

if [[ -z "$WILDFLY_USE_SSL" ]]; then
    # Default to use SSL (don't ask)
    use_ssl="Y"
else
    use_ssl=$WILDFLY_USE_SSL
fi

if [[ -z "$WILDFLY_USE_CASPORT" ]]; then
    # Default to NOT use SSL (don't ask)
    use_casport="N"
else
    use_casport=$WILDFLY_USE_CASPORT
fi

# Ask for http port, and store it for use when we startup JBoss
http_port_is_good=0
if is_upgrade ; then
    echo "Restoring HTTP port: $suggest_http_port"
    http_port=${suggest_http_port}
    http_port_is_good=$(expr "$http_port" : "^[0-9][0-9]*$")
fi
while [ "$http_port_is_good" == "0" ] ; do
    read -p "What port do you want to use for HTTP requests? [$suggest_http_port]: " http_port
    if [ -z "$http_port" ]; then
        http_port=${suggest_http_port}
    fi
    http_port_is_good=$(expr "$http_port" : "^[0-9][0-9]*$")
done

# Ask whether SSL is to be used, and store the setting for use when we startup JBoss
if [[ "$use_ssl" == "?" ]]; then
    use_ssl_is_good=0
    while [ "$use_ssl_is_good" == "0" ] ; do
        read -p "Do you want to use SSL? [y/N]: " use_ssl
        if [ -z "$use_ssl" ]; then
            use_ssl="N"
        fi
        use_ssl_is_good=$(expr "$use_ssl" : "^[YNyn]$")
    done
fi

use_ssl=$(echo $use_ssl | tr '[:upper:]' '[:lower:]')

if [ "$use_ssl" == "y" ]; then
    # Ask for https port, and store it for use when we startup JBoss
    https_port_is_good=0
    if is_upgrade ; then
        echo "Restoring HTTPS port: $suggest_https_port"
        https_port=${suggest_https_port}
        https_port_is_good=$(expr "$https_port" : "^[0-9][0-9]*$")
    fi
    while [ "$https_port_is_good" == "0" ] ; do
        read -p "What port do you want to use for HTTPS requests? [$suggest_https_port]: " https_port
        if [ -z "$https_port" ]; then
            https_port=$suggest_https_port
        fi
        https_port_is_good=$(expr "$https_port" : "^[0-9][0-9]*$")
    done

    if [[ "$use_casport" == "?" ]]; then
        # Ask whether CASPORT is to be used, and store the setting for use when we startup JBoss
        use_casport_is_good=0
        while [ "$use_casport_is_good" == "0" ] ; do
            read -p "Do you want to use CASPORT? [y/N]: " use_casport
            if [ -z "$use_casport" ]; then
                use_casport="N"
            fi
            use_casport_is_good=$(expr "$use_casport" : "^[YNyn]$")
        done
    fi
else
    https_port=$suggest_https_port
fi

# Enabled/Disable to use of the Java Security Manager
# Default to false for compatibility
# Set WILDFLY_SECMGR to "true" to enable use.
# WILDFLY_SECMGR will be assigned to ADJURE_WILDFLY_SECMGR in adjure_env.sh and used in the go script
if [[ -z "$WILDFLY_SECMGR" ]]; then
    WILDFLY_SECMGR="false"
fi
set_adjure_base_config ADJURE_WILDFLY_SECMGR $WILDFLY_SECMGR

# Save the HTTP port, and the HTTPS port if SSL is enabled, for use in the go script
set_adjure_base_config WILDFLY_HTTP_PORT $http_port
set_adjure_base_config WILDFLY_HTTPS_PORT $https_port
set_adjure_base_config WILDFLY_USE_SSL $use_ssl
set_adjure_base_config WILDFLY_USE_CASPORT $use_casport
if [ -n "$shiro_current_mode" ]; then
    set_adjure_base_config WILDFLY_SHIRO_CURRENT_MODE $shiro_current_mode
fi

echo "Updating shiro configuration..."
${SCRIPT_DIR}/set-shiro-mode.sh

source $ADJURE_CLASSIFICATION_INFO
function my_join() {
  local IFS=,
  echo "$*"
}

# TODO: This stuff really doesn't line up well with its TRS counterparts.
#       It's also not documented so I really don't know what I'm doing.
#       I do know how to make EY3 and EY5 work, so we'll start with that.
# class level and SCI don't have this problem, so at least we have that going.
function make_rel() {
  if [[ ${#ADJURE_BASE_CLASS_RELEASETO[@]} == 0 ]]; then
    echo ""
  else
    if [[ ${#ADJURE_BASE_CLASS_RELEASETO[@]} == 1 ]]; then
      if [[ "$ADJURE_BASE_CLASS_RELEASETO" == "EY3" ]]; then
        echo "USA AUS,GBR"
        return
      fi
      if [[ "$ADJURE_BASE_CLASS_RELEASETO" == "EY5" ]]; then
        echo "USA,AUS,CAN,GBR,NZL"
        return
      fi
      echo $ADJURE_BASE_CLASS_RELEASETO
    else
      my_join ${ADJURE_BASE_CLASS_RELEASETO[@]}
    fi
  fi
}

#
export J42_OVERRIDE_CLASS_LEVEL=$ADJURE_BASE_CLASS_LEVEL
export J42_OVERRIDE_SCI=$(my_join ${ADJURE_BASE_CLASS_COMPART[@]})
export J42_OVERRIDE_REL=$(make_rel)

echo "default.classification=$J42_OVERRIDE_CLASS_LEVEL" > $WILDFLY_J42_OVERRIDE_FILE
echo "default.sci=$J42_OVERRIDE_SCI" >> $WILDFLY_J42_OVERRIDE_FILE
echo "default.rel=$J42_OVERRIDE_REL" >> $WILDFLY_J42_OVERRIDE_FILE
echo "base.classification=U" >> $WILDFLY_J42_OVERRIDE_FILE
echo "base.owner=USA" >> $WILDFLY_J42_OVERRIDE_FILE
echo "base.dissem=FOUO" >> $WILDFLY_J42_OVERRIDE_FILE

chown COE:COE $WILDFLY_J42_OVERRIDE_FILE
chmod 664 $WILDFLY_J42_OVERRIDE_FILE
