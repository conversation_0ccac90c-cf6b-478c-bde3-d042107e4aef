#!/bin/bash

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_WILDFLY_TOOLS

get_widlfly_reconfigure_cli_input_file() {
  echo "/tmp/adjure_wildfly_reconfigure_cli.in" 
}

reconfig_input_file=$(get_widlfly_reconfigure_cli_input_file)

clear_wildfly_reconfig_input_file() {
    rm -f "$reconfig_input_file"
}

add_wildfly_reconfig_command() {
    echo $1 >>"$reconfig_input_file"
}

# add_infinispan_web_cache <container-name> <cache-name>
#
# the infinispan subsystem containers contain the infinispan caches that are referenced by the shiro
# integration components for Wildfly. Those components use JNDI to lookup the caches that shiro
# looks for. The shiro.ini file for each deployment specifies the cache for each authorization realm
# for that deployment, and for the active sessions maintained by the shiro session manager. By
# defining these caches through this method, the caches (and containerss) can be added dynamically
# when loading wildfly, before the application is deployed.
add_infinispan_web_cache() {
    echo "Configuring infinispan container '$1' and cache '$2'"
    add_wildfly_reconfig_command "if (outcome != success) of /subsystem=infinispan/cache-container=$1:read-resource"
    add_wildfly_reconfig_command "  /subsystem=infinispan/cache-container=$1:add(module=org.wildfly.clustering.web.infinispan)"
    add_wildfly_reconfig_command "end-if"
    add_wildfly_reconfig_command "if (outcome != success) of /subsystem=infinispan/cache-container=$1/local-cache=$2:read-resource"
    add_wildfly_reconfig_command "  /subsystem=infinispan/cache-container=$1/local-cache=$2:add"
    add_wildfly_reconfig_command "end-if"

    # Worked: Return 0, indicating success with changes (we have to assume something changed)

    return 0
}

# add_system_file_resource <logical-resource-name> <resource-path> <enable-directory-listing>
#
# This script ensures that the default server (name=default-server) default host (name=default-host)
# of the undertow subsystem includes a "location" resource with the specified logical-resource-name
# that "location" resource is configured with a "handler" attribute that references a handler of the
# same logical-resource-name. The handler is added (as a "file" type of handler) to the "handlers"
# of the undertow subsystem, with name=logical-resource-name, path=resource-path, and
# directory-listing=true if enable-directory-listing is present and specifies a value of true. 
#
add_system_file_resource() {
    # first, add the file handler

    add_wildfly_reconfig_command "if (outcome != success) of /subsystem=undertow/configuration=handler/file=$1:read-resource"
    if [[ "a$3" == "atrue" ]]; then
      echo "Configuring addition of system file resource: logical-name='$1'; path='$2'; enable-directory-listing=true"
      add_wildfly_reconfig_command "  /subsystem=undertow/configuration=handler/file=$1:add(path=$2,directory-listing=true)"
    else
      echo "Configuring addition of system file resource: logical-name='$1'; path='$2'; enable-directory-listing=false"
      add_wildfly_reconfig_command "  /subsystem=undertow/configuration=handler/file=$1:add(path=$2)"
    fi
    add_wildfly_reconfig_command "end-if"

    # now, add the location resource

    add_wildfly_reconfig_command "if (outcome != success) of /subsystem=undertow/server=default-server/host=default-host/location=$1:read-resource"
    add_wildfly_reconfig_command "  /subsystem=undertow/server=default-server/host=default-host/location=$1:add(handler=$1)"
    add_wildfly_reconfig_command "end-if"

    # Worked: Return 0, indicating success with changes (we have to assume something changed)

    return 0
}
