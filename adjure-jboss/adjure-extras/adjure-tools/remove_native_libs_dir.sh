#!/bin/bash

if [[ -z "$1" ]]; then
  echo "Must provide directory to remove"
  exit 1
fi

# See http://stackoverflow.com/questions/273909/how-do-i-manipulate-path-elements-in-shell-scripts
# Remove or replace an element of $1
#
#   $1 name of the shell variable to set (e.g. PATH)
#   $2 a ":" delimited list to work from (e.g. $PATH)
#   $3 the precise string to be removed/replaced
#   $4 the replacement string (use "" for removal)
function replace_path () {
    path=$1
    list=$2
    remove=$3
    replace=$4        # Allowed to be empty or unset

    export $path=$(echo "$list" | tr ":" "\n" | sed "s:^$remove\$:$replace:" |
                   tr "\n" ":" | sed 's|:$||')
}

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_SCRIPTING_COMMON

{
  flock -x 200

  export extradirs=$(get_adjure_base_config ADJURE_WILDFLY_EXTRA_NATIVE_LIBS_DIRS '')
  if [[ "$extradirs" != "" ]]; then
    replace_path extradirs $extradirs $1 ""
    extradirs=$(echo $extradirs | sed 's/::*/:/g')
    set_adjure_base_config ADJURE_WILDFLY_EXTRA_NATIVE_LIBS_DIRS $extradirs
  fi

} 200>/tmp/adjure_wildfly_add_native_libs_dir.sh.lock

rm -f /tmp/adjure_wildfly_add_native_libs_dir.sh.lock
