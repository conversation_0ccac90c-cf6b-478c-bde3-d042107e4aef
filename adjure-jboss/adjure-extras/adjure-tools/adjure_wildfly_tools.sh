#!/bin/bash
source /h/adjure/base/config/adjure_env.sh
source $ADJURE_SCRIPTING_COMMON

# adjure_wildfly_get_deployment_file_from_app_uuid <uuid>
function adjure_wildfly_get_deployment_file_from_app_uuid {
  dep_varname=deploymentfile_$(echo $1 | tr '-' '_')
  get_adjure_base_config $dep_varname
}

# adjure_wildfly_set_deployment_file_from_app_uuid <uuid> <file>
function adjure_wildfly_set_deployment_file_from_app_uuid {
  dep_varname=deploymentfile_$(echo $1 | tr '-' '_')
  set_adjure_base_config $dep_varname $2
}

# adjure_wildfly_remove_deployment_file_from_app_uuid <uuid>
function adjure_wildfly_remove_deployment_file_from_app_uuid {
  dep_varname=deploymentfile_$(echo $1 | tr '-' '_')
  remove_adjure_base_config $dep_varname
}

# adjure_wildfly_get_pre_deploy_script_from_app_uuid <uuid>
function adjure_wildfly_get_pre_deploy_script_from_app_uuid {
  dep_varname=pre_deploy_script_$(echo $1 | tr '-' '_')
  get_adjure_base_config $dep_varname
}

# adjure_wildfly_set_pre_deploy_script_from_app_uuid <uuid> <file>
function adjure_wildfly_set_pre_deploy_script_from_app_uuid {
  dep_varname=pre_deploy_script_$(echo $1 | tr '-' '_')
  set_adjure_base_config $dep_varname $2
}

# adjure_wildfly_remove_pre_deploy_script_from_app_uuid <uuid>
function adjure_wildfly_remove_pre_deploy_script_from_app_uuid {
  dep_varname=pre_deploy_script_$(echo $1 | tr '-' '_')
  remove_adjure_base_config $dep_varname
}

# adjure_wildfly_get_post_undeploy_script_from_app_uuid <uuid>
function adjure_wildfly_get_post_undeploy_script_from_app_uuid {
  dep_varname=post_undeploy_script_$(echo $1 | tr '-' '_')
  get_adjure_base_config $dep_varname
}

# adjure_wildfly_set_post_undeploy_script_from_app_uuid <uuid> <file>
function adjure_wildfly_set_post_undeploy_script_from_app_uuid {
  dep_varname=post_undeploy_script_$(echo $1 | tr '-' '_')
  set_adjure_base_config $dep_varname $2
}

# adjure_wildfly_remove_post_undeploy_script_from_app_uuid <uuid>
function adjure_wildfly_remove_post_undeploy_script_from_app_uuid {
  dep_varname=post_undeploy_script_$(echo $1 | tr '-' '_')
  remove_adjure_base_config $dep_varname
}

# adjure_wildfly_get_deployment_file_from_app_inst_ref_id <app_inst_ref_id>
function adjure_wildfly_get_deployment_file_from_app_inst_ref_id {
  uuid=$(echo $1 | grep -oP 'uniqueIdentifier=[^,]*' | sed 's/uniqueIdentifier=//')
  adjure_wildfly_get_deployment_file_from_app_uuid $uuid
}

# adjure_wildfly_get_pre_deploy_script_from_app_inst_ref_id <app_inst_ref_id>
function adjure_wildfly_get_pre_deploy_script_from_app_inst_ref_id {
  uuid=$(echo $1 | grep -oP 'uniqueIdentifier=[^,]*' | sed 's/uniqueIdentifier=//')
  adjure_wildfly_get_pre_deploy_script_from_app_uuid $uuid
}

# adjure_wildfly_get_post_undeploy_script_from_app_inst_ref_id <app_inst_ref_id>
function adjure_wildfly_get_post_undeploy_script_from_app_inst_ref_id {
  uuid=$(echo $1 | grep -oP 'uniqueIdentifier=[^,]*' | sed 's/uniqueIdentifier=//')
  adjure_wildfly_get_post_undeploy_script_from_app_uuid $uuid
}

# adjure_wildfly_install_deployment <war_file_full_path> <exploded_flag>
# Places a deployment in the staging directory, and bumps the EJB thread count by 5
# if <exploded_flag> is not provided, or is true, the an exploded version of the
# deployment is created alongside the deployment file in the staging directory. To
# prevent this, specify any value except "y" or "Y" for this flag
function adjure_wildfly_install_deployment {
  local staging_dir=$(get_adjure_base_config ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR)
  cp -f $1 $staging_dir
  if [[ "x$2" == "x" || "$2" == "y" || "$2" == "Y" ]]; then
      local deployment_file=$(basename $1)
      local deployment_file_full=$staging_dir/$deployment_file
      local explode_staging_dir=$staging_dir/exploded
      mkdir -p $explode_staging_dir
      local explode_dir=$explode_staging_dir/$deployment_file
      $(adjure_wildfly_explode_deployment $deployment_file_full $explode_dir)
  fi
  ejb_thread_count=$(get_adjure_base_config ADJURE_WILDFLY_EJB_THREAD_COUNT)
  ejb_thread_count=$(($ejb_thread_count + 5))
  set_adjure_base_config ADJURE_WILDFLY_EJB_THREAD_COUNT $ejb_thread_count
}

# adjure_wildfly_uninstall_deployment <war_file_basename>
# Removes a deployment from the staging directory, and reduces the EJB thread count by 5
function adjure_wildfly_uninstall_deployment {
  local staging_dir=$(get_adjure_base_config ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR)
  rm -rf $staging_dir/exploded/$1
  rm -f $staging_dir/$1
  ejb_thread_count=$(get_adjure_base_config ADJURE_WILDFLY_EJB_THREAD_COUNT)
  ejb_thread_count=$(($ejb_thread_count - 5))
  set_adjure_base_config ADJURE_WILDFLY_EJB_THREAD_COUNT $ejb_thread_count
}

# adjure_wildfly_install_bin_ext <bin_ext_file_full_path>
function adjure_wildfly_install_bin_ext {
  cp -f $1 $(get_adjure_base_config ADJURE_WILDFLY_BIN_DIR)/ext
}

# adjure_wildfly_uninstall_bin_ext <bin_ext_file_basename>
function adjure_wildfly_uninstall_bin_ext {
  rm -f $(get_adjure_base_config ADJURE_WILDFLY_BIN_DIR)/ext/$1
}

# adjure_wildfly_install_config_ext <config_ext_file_full_path>
function adjure_wildfly_install_config_ext {
  cp -f $1 $(get_adjure_base_config ADJURE_WILDFLY_BIN_DIR)/config-ext  
}

# adjure_wildfly_uninstall_config_ext <config_ext_file_basename>
function adjure_wildfly_uninstall_config_ext {
  rm -f $(get_adjure_base_config ADJURE_WILDFLY_BIN_DIR)/config-ext/$1  
}

# adjure_wildfly_add_mem_req <req>
# req is something like 4096 for 4096 MB RAM
function adjure_wildfly_add_mem_req {
  # default (start at) 2048 MB
  min_mem=$(get_adjure_base_config ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG)
  min_mem=$(($min_mem + $1))
  set_adjure_base_config ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG $min_mem
}

# adjure_wildfly_pre_load_jicd42_setup
# setup the environment to ensure that it is correctly configured for a deployment that uses JICD42
# this method should be called from a bin/ext script for each deployment that uses JICD42
function adjure_wildfly_pre_load_jicd42_setup {
  # need to set core.envelope.useWoodstox to false to prevent the appgeo and silkwave libraries
  # from changing the XML parser to a parser that is not on the classpath for all deployments
  # without setting this property to false, a class not found exception occurs on deployments that
  # don't use JICD42 appgeo and silkwave libraries
  adjure_wildfly_set_system_property core.envelope.useWoodstox false
}

# See http://stackoverflow.com/questions/273909/how-do-i-manipulate-path-elements-in-shell-scripts
# Remove or replace an element of $1
# This is an adaptation of that, used for a shell variable with " -d" as the delimiter
#
#   $1 name of the shell variable to be changed (e.g. FOO)
#   $2 a " -d" delimited list to work from (e.g. $BAR)
#   $3 the precise string to be removed
#   $4 the replacement string (use "" for removal)
function replace_property {
    list=$1
    prop_name=$2
    prop_value=$3 # allowed to be empty, or unset (to clear the property setting)

    local var=$(echo "$list" | sed "s/\s*-D/\n/g")
    if [[ "$prop_value" != "" ]]; then
        var=$(echo "$var" | sed "s/^$prop_name=.*$/$prop_name=\"$prop_value\"/")
    else
        var=$(echo "$var" | sed "s/^$prop_name=.*$//")
    fi
    var=$(echo "$var" | xargs -I % echo -n " -D"%)
    if [[ "$prop_value" == "" ]]; then
        var=$(echo "$var" | sed "s/\s-D\s//g")
    fi
    echo $var
}

# adjure_wildfly_clear_system_property <property_name>
function adjure_wildfly_clear_system_property {
    {
        flock -x 200

        local system_props=$(get_adjure_base_config ADJURE_WILDFLY_SYSTEM_PROPS '')
        if [[ "$system_props" != "" ]]; then
            system_props=$(replace_property "$system_props" $1 "")
            set_adjure_base_config ADJURE_WILDFLY_SYSTEM_PROPS "$system_props"
        fi

    } 200>/tmp/adjure_wildfly_remove_system_property.sh.lock

    rm -f /tmp/adjure_wildfly_remove_system_property.sh.lock
}

# Explode the deployment archive into a "exploded" sub-directory from the main deployment file
function adjure_wildfly_explode_deployment {
    local full_path_file_to_explode=$1
    local full_path_directory_for_exploded=$2
    if [ ! -d $full_path_directory_for_exploded ]; then
        unzip -q $full_path_file_to_explode -d $full_path_directory_for_exploded
        # COE should own the files/directories, and the files/directories should be in the COE group
        chown -R COE:COE $full_path_directory_for_exploded
        # only the owner should be able to change the files/directories
        chmod -R go-w $full_path_directory_for_exploded
    fi
}

# adjure_wildfly_set_system_property <property_name> <value>
# if <value> is not provided, the property will be cleared
function adjure_wildfly_set_system_property {
    {
        flock -x 200

        local system_props=$(get_adjure_base_config ADJURE_WILDFLY_SYSTEM_PROPS '')
        if [[ -z "$system_props" ]]; then
            if [[ "$2" != "" ]]; then
                system_props="-D$1=$2"
            fi
        else
            local exists=$(echo "$system_props" | sed "s/^.*-D$1=.*$/found/")
            if [[ "$exists" != "found" ]]; then
                if [[ "$2" != "" ]]; then
                    system_props="$system_props -D$1=$2"
                fi
            else
                if [[ "$2" != "" ]]; then
                    system_props=$(replace_property "$system_props" $1 "$2")
                else
                    system_props=$(replace_property "$system_props" $1 "")
                fi
            fi
        fi

        set_adjure_base_config ADJURE_WILDFLY_SYSTEM_PROPS "$system_props"

    } 200>/tmp/adjure_wildfly_remove_system_property.sh.lock

    rm -f /tmp/adjure_wildfly_remove_system_property.sh.lock
}
