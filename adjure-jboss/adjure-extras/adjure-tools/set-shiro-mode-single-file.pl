#!/usr/bin/perl
use strict;
use warnings FATAL => 'all';

my $script_name = $0;

my $numargs = @ARGV;
if ($numargs != 3) {
    usage_and_exit(1);
}

my $filename_in = shift @ARGV;
my $filename_out = shift @ARGV;
my $enable_mode_str = shift @ARGV;

$enable_mode_str =~ s/^--(.*)$/$1/;
if ($enable_mode_str !~ m/^auto$/ && $enable_mode_str !~ m/^pki$/ && $enable_mode_str !~ m/^pkitest$/ && $enable_mode_str !~ m/^casport$/) {
    usage_and_exit(2);
}

if ($enable_mode_str =~ m/^auto$/) {
    if (defined($ENV{'WILDFLY_SHIRO_MODE_ALLOW_AUTO'})) {
        my $allow_auto = lc $ENV{'WILDFLY_SHIRO_MODE_ALLOW_AUTO'} eq 'y';
        if ($allow_auto == 0) {
            print "System configuration does not allow 'auto' Shiro configuration\n";
            exit 2;
        }
    }
}

if ($enable_mode_str =~ m/^pki$/ || $enable_mode_str =~ m/^pkitest$/ || $enable_mode_str =~ m/^casport$/) {
    if (defined($ENV{'WILDFLY_SHIRO_MODE_ALLOW_PKI'})) {
        my $allow_pki = lc $ENV{'WILDFLY_SHIRO_MODE_ALLOW_PKI'} eq 'y';
        if ($allow_pki == 0) {
            print "System configuration does not allow '$enable_mode_str' Shiro configuration\n";
            exit 2;
        }
    }
    if ($enable_mode_str =~ m/^pkitest$/) {
        if (defined($ENV{'WILDFLY_SHIRO_MODE_ALLOW_PKITEST'})) {
            my $allow_pkitest = lc $ENV{'WILDFLY_SHIRO_MODE_ALLOW_PKITEST'} eq 'y';
            if ($allow_pkitest == 0) {
                print "System configuration does not allow 'pkitest' Shiro configuration\n";
                exit 2;
            }
        }
    }
    if ($enable_mode_str =~ m/^casport$/) {
        if (defined($ENV{'WILDFLY_SHIRO_MODE_ALLOW_CASPORT'})) {
            my $allow_casport = lc $ENV{'WILDFLY_SHIRO_MODE_ALLOW_CASPORT'} eq 'y';
            if ($allow_casport == 0) {
                print "System configuration does not allow 'casport' Shiro configuration\n";
                exit 2;
            }
        }
    }
}

my $mode = 0;
if ($enable_mode_str =~ m/^auto$/) {
    $mode = 0;
    print "Enabling shiro 'auto' configuration in: $filename_out\n";
} elsif ($enable_mode_str =~ m/^pkitest$/) {
    $mode = 1;
    print "Enabling shiro 'pkitest' configuration in: $filename_out\n";
} elsif ($enable_mode_str =~ m/^pki$/) {
    $mode = 2;
    print "Enabling shiro 'pki' configuration in: $filename_out\n";
} else {
    $mode = 3;
    print "Enabling shiro 'casport' configuration in: $filename_out\n";
}

sub usage_and_exit {
    print "Usage: $script_name read_filename write_filename [--auto|--pkitest|--pki|--casport]\n";
    exit shift;
}

sub get_section_start {
    my ($line, $section_descriptor) = @_;
    my $nested_descriptors = $section_descriptor->[2];
    if (defined($nested_descriptors)) {
        foreach (@$nested_descriptors) {
            my $section = $_;
            my $match = "^#\\s\\s*\@\@$section->[1]\@\@.*\$";
            # print "Attempting to match line ($line) against pattern ($match)\n";
            if ($line =~ m/$match/) {
                # print "Line matches\n";
                return $section;
            }
        }
    }
    return undef;
}

sub get_section_start_line_type {
    my @descriptor_enable_modes = @_;
    # print "Section enable modes: @descriptor_enable_modes\n";
    foreach (@descriptor_enable_modes) {
        my $descriptor_mode = $_;
        # print "Checking descriptor-mode=$descriptor_mode; mode=$mode\n";
        if ($descriptor_mode == $mode) {
            # print "Found mode match in descriptor: descriptor-mode=$descriptor_mode; mode=$mode\n";
            return 0;
        }
    }
    # print "No mode match in descriptor\n";
    return 1;
}

sub is_section_end {
    my ($line, $section_name) = @_;
    my $match = "^#\\s\\s*\@\@$section_name\@\@.*\$";
    # print "Attempting to match line ($line) against pattern ($match)\n";
    if ($line =~ m/$match/) {
        # end section line
        return 1;
    }
    return 0;
}

# Our state is one of the following:
# - 0: as-is
# - 1: enable (uncomment property lines)
# - 2: disable (comment property lines)
my $active_state = 0;

# A line falls into one of the following categories:
# - 0: start enable section line
# - 1: start disable section line
# - 2: end current section line
# - 3: uncommented property line
# - 4: commented property line
# - 5: normal line
sub get_line_type {
    my ($line, $section_descriptor) = @_;

    # print "Calculating line type\n";
    # If disabling property lines, we don't want to look for the start of a section, since we are inside of a
    #  section that is being disabled
    if ($active_state != 2) {
        my $result = get_section_start($line, $section_descriptor);
        if (defined($result)) {
            # print "Found new section: $result->[1]\n";
            if (defined($result->[0])) {
                my $enable_modes = @$result[0];
                return get_section_start_line_type(@$enable_modes);
            } else {
                # print "Section always disabled";
                return 1;
            }
        }
    }

    if (defined($section_descriptor->[1]) && is_section_end($line, $section_descriptor->[1])) {
        # print "Found end of section: $section_descriptor->[1]\n";
        # section end line
        return 2;
    } elsif ($line =~ m/^\s*#\s*[^\s#]+\s*=\s*[^\s#]+/) {
        # print "Found commented property line: '$line'\n";
        # commented property line
        return 4;
    } elsif ($line =~ m/^\s*[^\s#]+\s*=\s*[^\s#]+/) {
        # print "Found uncommented property line: '$line'\n";
        # uncommented property line
        return 3;
    } else {
        # print "Found normal line: '$line'\n";
        # normal line
        return 5;
    }
}

# A section descriptor is an array with the following structure:
# - descriptor[0]: an array of modes; if the selected mode matches any, then we should switch state to enable
# - descriptor[1]: a scalar value that is the section delimiter
# - descriptor[2]: an array of descriptors (may be empty); each descriptor describes a section that could be enabled or disabled with this section is enabled
#
my $start_section = [
    undef,
    "Main Descriptor",
    [
        [
            [1, 2, 3], # when PKI (test or prod) or CASPORT, but not when auto
            "PKI",
            [
                [
                    [2, 3], # when CASPORT or production PKI
                    "CERT FAILURE PROD",
                    undef,
                ],
                [
                    [1], # when PKI test mode
                    "CERT FAILURE TEST",
                    undef,
                ],
                [
                    [1, 2], # when PKI (test or prod), but not when CASPORT
                    "REALM PKI",
                    undef,
                ],
                [
                    [3], # only when CASPORT (would also be enabled for CASPORT CR, if that were supported)
                    "REALM CASPORT",
                    [
                        [
                            [3], # only when CASPORT with user's attributes
                            "REALM CASPORT ATTR",
                            undef,
                        ],
                        [
                            undef, # never enabled
                            "REALM CASPORT CR",
                            undef,
                        ],
                    ],
                ],
            ],
        ],
        [
            [0], # only when auto
            "AUTO",
            undef,
        ],
    ],
];

# - section descriptor array
my $active_section_descriptor = $start_section;
my @section_descriptor_stack = ();
my @state_stack = ();

open(RFH, "<$filename_in") or die "Unable to read $filename_in";
open(WFH, ">$filename_out") or die "Unable to create $filename_out";

while (<RFH>) {
    chomp;
    my $line = $_;

    my $line_type = get_line_type($line, $active_section_descriptor);
    # print "Found line type=$line_type\n";
    # - 0: start enable section line
    # - 1: start disable section line
    # - 2: end current section line
    # - 3: uncommented property line
    # - 4: commented property line
    # - 5: normal line
    my $result_line;
    if ($line_type == 0 || $line_type == 1) {
        my $section = get_section_start($line, $active_section_descriptor);
        # - push the current section descriptor
        # - push the current state
        # print "Pushing current descriptor (@$active_section_descriptor[1]) and active state ($active_state)\n";
        push @section_descriptor_stack, $active_section_descriptor;
        push @state_stack, $active_state;
        $active_section_descriptor = $section;
        $result_line = $line;
        if ($line_type == 0) {
            $active_state = 1;
        } else {
            $active_state = 2;
        }
        # print "New descriptor (@$active_section_descriptor[1])\n";
        # print "New active state=$active_state\n";
    } elsif ($line_type == 2) {
        # print "Found end of section; popping state and descriptor\n";
        $active_section_descriptor = pop @section_descriptor_stack;
        $active_state = pop @state_stack;
        # print "New descriptor (@$active_section_descriptor[1])\n";
        # print "New active state=$active_state\n";
        $result_line = $line;
    } else {
        if ($active_state == 0 || $line_type == 5) {
            # print "as-is state ($active_state) or normal line ($line_type); no change to line\n";
            $result_line = $line;
        } else {
            if ($line_type == 3) {
                # print "Uncommented property line ($line_type)\n";
                if ($active_state == 1) {
                    # print "enabling properties ($active_state); no change to line\n";
                    $result_line = $line;
                } else {
                    # print "disabling properties ($active_state); comment line\n";
                    # we are disabling lines, and this is an uncommented property, so comment it
                    $result_line = $line;
                    $result_line =~ s/(^.*)$/#$1/;
                }
            } elsif ($line_type == 4) {
                # print "Commented property line ($line_type)\n";
                if ($active_state == 1) {
                    # print "enabling properties ($active_state); uncomment line\n";
                    # we are enabling lines, and this is a commented property, so uncomment it
                    $result_line = $line;
                    $result_line =~ s/^#(.*)$/$1/;
                } else {
                    # print "disabling properties ($active_state); no change to line\n";
                    $result_line = $line;
                }
            }
        }
    }

    # enable/disable ssl based on auto/non-auto mode
    if ($result_line =~ m/^\s*ssl.enabled\s*=.*$/) {
        if ($mode == 0) {
            # auto mode - disable
            $result_line =~ s/^(\s*ssl.enabled\s*=\s*).*$/${1}false/;
        } else {
            # non-auto mode - enable
            $result_line =~ s/^(\s*ssl.enabled\s*=\s*).*$/${1}true/;
        }
    }

    # print "Writing line ($result_line)\n";
    print WFH "$result_line\n";
}

close(WFH);
close(RFH);
