#!/bin/bash

doEcho() {
  echo "$1"
}

usage() {
  doEcho "Usage: $0 {start|status|stop}"
  doEcho "   Status return code 1 means try again later. 2 means failed, 0 means success."
  doEcho "   Note: APP_INST_REF_ID env var must be set to app installation dn for service to deploy."
  doEcho "         Service referenced must be integrated with adjure ecosystem"
}

if [ "$1" = "" ]; then
  usage
  exit 2
fi

if [ "$APP_INST_REF_ID" = "" ]; then
  usage
  exit 2
fi

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_WILDFLY_TOOLS

WILDFLY_CLI="$ADJURE_WILDFLY_HOME/bin/jboss-cli.sh -u=sysadmin -p=PassMe12#$"
DEPLOYMENT_NAME=$(adjure_wildfly_get_deployment_file_from_app_inst_ref_id $APP_INST_REF_ID)
DEPLOYMENT_FULL_PATH=$ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR/$DEPLOYMENT_NAME

# TODO: Put this in a file with better permissions
CONN_STUFF="--anyauth -u sysadmin:PassMe12#$"

preDeploy() {
    PRE_DEPLOY_SCRIPT=$(adjure_wildfly_get_pre_deploy_script_from_app_inst_ref_id $APP_INST_REF_ID)
    if [ -n "$PRE_DEPLOY_SCRIPT" ]; then
        doEcho "$DEPLOYMENT_NAME: Running pre-deploy script: $PRE_DEPLOY_SCRIPT"
        $PRE_DEPLOY_SCRIPT
    else
        doEcho "$DEPLOYMENT_NAME: No pre-deploy script"
    fi
}

stop() {
    doEcho "$DEPLOYMENT_NAME: Undeploying"
    # Instead of everything below up to RESULT=$?, you could run this to use the jboss-cli (very slow)
    #$WILDFLY_CLI --connect --command="undeploy $DEPLOYMENT_NAME"

    curl -s -S -H "content-Type: application/json" -d '{"operation":"undeploy", "address":[{"deployment":"'$DEPLOYMENT_NAME'"}]}' $CONN_STUFF http://localhost:9990/management >/dev/null 2>&1
    curl -s -S -H "content-Type: application/json" -d '{"operation":"remove", "address":[{"deployment":"'$DEPLOYMENT_NAME'"}]}' $CONN_STUFF http://localhost:9990/management >/dev/null 2>&1
    RESULT=$?

    if [[ "$RESULT" == "0" ]]; then
        POST_UNDEPLOY_SCRIPT=$(adjure_wildfly_get_post_undeploy_script_from_app_inst_ref_id $APP_INST_REF_ID)
        if [ -n "$POST_UNDEPLOY_SCRIPT" ]; then
            doEcho "$DEPLOYMENT_NAME: Running post-undeploy script: $POST_UNDEPLOY_SCRIPT"
            $POST_UNDEPLOY_SCRIPT
        else
            doEcho "$DEPLOYMENT_NAME: No post-undeploy script"
        fi
    fi
    return $RESULT
}

status() {
    status_result=$(curl -s -S -H 'content-Type: application/json' -d '{"operation":"read-attribute", "address":[{"deployment":"'$DEPLOYMENT_NAME'"}], "name":"status"}' $CONN_STUFF http://localhost:9990/management 2>/dev/null)

    if echo "$status_result" | grep outcome | grep -q failed; then
        return 2
    fi

    if echo "$status_result" | grep result | grep -q "OK"; then
        return 0
    fi

    if echo "$status_result" | grep result | grep -q "FAILED"; then
        return 2
    fi
    return 1
}
