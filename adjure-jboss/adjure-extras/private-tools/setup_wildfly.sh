#!/bin/bash

if [[ -z "$RPM_INSTALL_PREFIX" ]]; then
  echo "This script is meant to be called only from RPM installation"
fi

rm -f /tmp/console.log

source /h/adjure/base/config/adjure_env.sh

source $ADJURE_SCRIPTING_COMMON
set_adjure_base_config ADJURE_WILDFLY_BASE                                "$RPM_INSTALL_PREFIX/adjure-jboss"
set_adjure_base_config ADJURE_WILDFLY_HOME                                "$RPM_INSTALL_PREFIX/adjure-jboss/wildfly"
set_adjure_base_config ADJURE_WILDFLY_BIN_DIR                             "$RPM_INSTALL_PREFIX/adjure-jboss/wildfly/bin"
set_adjure_base_config ADJURE_WILDFLY_CLI                                 "$RPM_INSTALL_PREFIX/adjure-jboss/wildfly/bin/jboss-cli.sh --connect -u=sysadmin -p=PassMe12#$"
set_adjure_base_config ADJURE_WILDFLY_DATADIR                             "$RPM_INSTALL_PREFIX/adjure-jboss/wildfly/standalone"
set_adjure_base_config ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR              "$RPM_INSTALL_PREFIX/adjure-jboss/deployments"
set_adjure_base_config ADJURE_WILDFLY_TOOLS_PATH                          "$RPM_INSTALL_PREFIX/adjure-jboss/adjure-extras/adjure-tools"
set_adjure_base_config ADJURE_WILDFLY_TOOLS                               "$RPM_INSTALL_PREFIX/adjure-jboss/adjure-extras/adjure-tools/adjure_wildfly_tools.sh"
set_adjure_base_config ADJURE_WILDFLY_RECONFIGURE_TOOLS                   "$RPM_INSTALL_PREFIX/adjure-jboss/adjure-extras/adjure-tools/adjure_wildfly_reconfigure_tools.sh"
set_adjure_base_config ADJURE_WILDFLY_MODULES_PATH                        "$RPM_INSTALL_PREFIX/adjure-jboss/wildfly/adjure-modules"
set_adjure_base_config ADJURE_WILDFLY_ADD_NATIVE_LIBS_DIR                 "$RPM_INSTALL_PREFIX/adjure-jboss/adjure-extras/adjure-tools/add_native_libs_dir.sh"
set_adjure_base_config ADJURE_WILDFLY_REMOVE_NATIVE_LIBS_DIR              "$RPM_INSTALL_PREFIX/adjure-jboss/adjure-extras/adjure-tools/remove_native_libs_dir.sh"
set_adjure_base_config ADJURE_WILDFLY_INSTALL_CONFIG_CERT_STORE_PWD_EXT   "$RPM_INSTALL_PREFIX/adjure-jboss/adjure-extras/adjure-tools/install-config-cert-store-pwd-ext.sh"
set_adjure_base_config ADJURE_WILDFLY_UNINSTALL_CONFIG_CERT_STORE_PWD_EXT "$RPM_INSTALL_PREFIX/adjure-jboss/adjure-extras/adjure-tools/uninstall-config-cert-store-pwd-ext.sh"
set_adjure_base_config ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG                  2048
set_adjure_base_config ADJURE_WILDFLY_EJB_THREAD_COUNT                    10
set_adjure_base_config WILDFLY_J42_OVERRIDE_FILE                          "$RPM_INSTALL_PREFIX/adjure-jboss/wildfly/standalone/configuration/j42_override.properties"
set_adjure_base_config WILDFLY_KEYSTORE_PWD                               "secret"
set_adjure_base_config WILDFLY_TRUSTSTORE_PWD                             "secret"
