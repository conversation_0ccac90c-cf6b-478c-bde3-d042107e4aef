#!/bin/bash

source /h/adjure/base/config/adjure_env.sh

source $ADJURE_SCRIPTING_COMMON

if [ -e "${WILDFLY_J42_OVERRIDE_FILE}" ]; then
    rm -f "${WILDFLY_J42_OVERRIDE_FILE}"
fi

remove_adjure_base_config ADJURE_WILDFLY_BASE
remove_adjure_base_config ADJURE_WILDFLY_HOME
remove_adjure_base_config ADJURE_WILDFLY_BIN_DIR
remove_adjure_base_config ADJURE_WILDFLY_CLI
remove_adjure_base_config ADJURE_WILDFLY_DATADIR
remove_adjure_base_config ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR
remove_adjure_base_config ADJURE_WILDFLY_TOOLS_PATH
remove_adjure_base_config ADJURE_WILDFLY_TOOLS
remove_adjure_base_config ADJURE_WILDFLY_RECONFIGURE_TOOLS
remove_adjure_base_config ADJURE_WILDFLY_MODULES_PATH
remove_adjure_base_config ADJURE_WILDFLY_ADD_NATIVE_LIBS_DIR
remove_adjure_base_config ADJURE_WILDFLY_REMOVE_NATIVE_LIBS_DIR
remove_adjure_base_config ADJURE_WILDFLY_INSTALL_CONFIG_CERT_STORE_PWD_EXT
remove_adjure_base_config ADJURE_WILDFLY_UNINSTALL_CONFIG_CERT_STORE_PWD_EXT
remove_adjure_base_config ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG
remove_adjure_base_config ADJURE_WILDFLY_EJB_THREAD_COUNT
remove_adjure_base_config WILDFLY_J42_OVERRIDE_FILE
remove_adjure_base_config WILDFLY_KEYSTORE_PWD
remove_adjure_base_config WILDFLY_TRUSTSTORE_PWD
