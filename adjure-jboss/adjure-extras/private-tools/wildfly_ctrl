#!/bin/bash

doEcho() {
  echo "$1"
}

usage() {
  doEcho "Usage: $0 {start|stop|status}"
  doEcho "   Status return code 1 means try again later; 0 means success; any other code means failure"
}

if [ "$1" = "" ]; then
  usage
  exit 2
fi

# error if not run as root or opsys
username=$(whoami)
if [ "$username" != "root" ] && [ "$username" != "opsys" ] ; then
    doEcho "You may only run this command as root or opsys. (You are $username.)"
    exit 2
fi

source /h/adjure/base/config/adjure_env.sh

if [[ "$ADJURE_WILDFLY_BASE" == "" ]]; then
  doEcho "ADJURE_WILDFLY_BASE not defined"
  exit 2
fi

source $ADJURE_WILDFLY_TOOLS
source $ADJURE_WILDFLY_RECONFIGURE_TOOLS

#
# The application that we run to start Wildfly
#
exec="$ADJURE_WILDFLY_BIN_DIR/go"

#
# Used as the name of the application in echo statements
#
prog="wildfly"
#
# The user that we use to run Wildfly
#
user="opsys"
#
# Max number of loop iterations to perform (each testing whether app has
# started) before abandoning wait
#
startup_wait_iterations=32
#
# Max number of iterations to perform (each testing whether app has shutdown)
# before abandoning wait
#
shutdown_wait_iterations=8
#
# Amount of time to wait (in microseconds) between each start status check
#
startup_wait_increment_microsec=125000

#
# Check for running wildfly process
#   0 = wildfly is running
#
isRunning() {
    #
    # find processes with jboss in them, excluding the grep, and the service query itself,
    # and the shell command the service query spawned...etc etc.
    #
    pid=`ps axf | grep "[j]ava .* -server .*${ADJURE_WILDFLY_BASE}" | awk '{print $1}'`
    if [ -n "$pid" ] ; then
        return 0
    fi
    return 1;
}

isAccessible() {
    #
    # return 2 if app NOT running
    #
    isRunning
    retval=$?
    if [ $retval -ne 0 ]; then
        return 2
    fi
    doEcho "Checking if ${prog} is accessible"
    $ADJURE_WILDFLY_CLI --command=ls 2>&1
    retval=$?
    if [ $retval -ne 0 ]; then
        doEcho "${prog} is NOT accessible"
        return 1
    fi
    doEcho "${prog} is accessible"
    return 0
}

configure_http_listener_redirect() {
    reconfig_input_file=$(get_widlfly_reconfigure_cli_input_file)
    doEcho "Configuring HTTP redirection setting"
    if [[ $WILDFLY_USE_SSL == "y" ]]; then
      add_wildfly_reconfig_command "if (outcome == success && result == undefined) of /subsystem=undertow/server=default-server/http-listener=default:read-attribute(name=redirect-socket)"
      add_wildfly_reconfig_command "  /subsystem=undertow/server=default-server/http-listener=default:write-attribute(name=redirect-socket,value=https)"
      add_wildfly_reconfig_command "end-if"
    else
      add_wildfly_reconfig_command "if (outcome == success && result != undefined) of /subsystem=undertow/server=default-server/http-listener=default:read-attribute(name=redirect-socket)"
      add_wildfly_reconfig_command "  /subsystem=undertow/server=default-server/http-listener=default:undefine-attribute(name=redirect-socket)"
      add_wildfly_reconfig_command "end-if"
    fi
    # Worked: Return 0, indicating success with changes (we have to assume something changed)
    return 0
}

#
# Reconfigure wildfly as necessary
#   0 = wildfly was reconfigured
#
reconfigure() {
    clear_wildfly_reconfig_input_file
    configure_http_listener_redirect
    retval=$?
    # 0 means all ok, changes were applied
    # 1 means all ok, no changes made
    # > 1 means a failure
    if [ $retval -gt 1 ]; then
        doEcho "Unable to configure http redirect"
        return $retval
    fi

    #
    # Run all the extension scripts
    if [ -e "$ADJURE_WILDFLY_BIN_DIR/config-ext" ]; then
        for f in $(find "$ADJURE_WILDFLY_BIN_DIR/config-ext" -maxdepth 1 -type f); do
            doEcho "Running ${prog} configuration extension script: $f"
            . $f
            config_retval=$?
            if [ $config_retval -eq 0 ]; then
                retval=0
            else
                # > 1 means a failure
                if [ $config_retval -gt 1 ]; then
                    doEcho "Configuration failure ($config_retval) reported by configuration extension script"
                    return $config_retval
                fi
            fi
        done
    fi

    if [ $retval -eq 0 ]; then
        reconfig_input_file=$(get_widlfly_reconfigure_cli_input_file)
        echo "Reconfiguring Wildfly using commands from reconfigure script (${reconfig_input_file}):"
        cat $reconfig_input_file
        $ADJURE_WILDFLY_CLI --file=$reconfig_input_file
        retval=$?
        clear_wildfly_reconfig_input_file
        if [ $retval -eq 0 ]; then
            echo "Wildfly reconfiguration succeeded"
        else
            # Didn't work: Return 2, indicating failure, possibly with changes
            echo "Wildfly reconfiguration failed"
            return 2
        fi
    fi
    return $retval
}

start() {
    #
    # If the script we need to run does not exist, then return with status 5
    #
    if [ -x "$exec" ]; then
        doEcho "Starting ${prog}..."
        cd $ADJURE_WILDFLY_BIN_DIR
    
        #
        # If we're root, then use sudo to run the command as the correct user.
        # Otherwise, just run the command 
        #
        if [ "$username" == "root" ] ; then
            doEcho "Running $exec (through sudo) as '$user' in $(pwd)"
            # -E to retain the current environment (default in RHEL 6, not default in RHEL 7, so state it explicitly)
            sudo -E -u $user $exec
        else
            #
            # Clear the LD_LIBRARY_PATH value, ensuring the go script sets the
            #   entire value (deterministic behavior)
            #
            unset LD_LIBRARY_PATH
            doEcho "Running $exec (directly) as 'opsys' in $(pwd)"
            $exec
        fi
    
        doEcho "Waiting for ${prog} to be accessible..."    
        #
        # wait in 1/8 sec increments & check to see if we are running and accessible
        #   on each iteration
        #
        count=0
        until [ $count -gt $startup_wait_iterations ]
        do
            isAccessible
            retval=$?
            if [ $retval -eq 0 ]; then
                break
            fi
            usleep $startup_wait_increment_microsec
            let count=$count+1;
        done
    
        isAccessible
        retval=$?
        if [ $retval -eq 0 ]; then
            #
            # Now, modify the configuration
            #
            doEcho "${prog} is now accessible; reconfiguring"    
            reconfigure
            retval=$?
            if [ $retval -eq 0 ]; then
                # This reload request could report a non-zero status code because WF does not re-start in some
                #   pre-determined amout of time. We don't want to fail when that occurs. We want status to be
                #   used to report progress, so exit with 0 (fall out of case)
                doEcho "Reloading ${prog} after re-configuration"
                $ADJURE_WILDFLY_CLI "reload" 2>&1
                doEcho "${prog} reloaded"
                #
                # Always a good status if we get here
                #
                retval=0
            else
                if [ $retval -gt 1 ]; then
                    doEcho "Unable to successfully complete re-configuration"
                    stop
                else
                    doEcho "Skipping reload (no re-configuration necessary)"
                    #
                    # Always a good status if we get here
                    #
                    retval=0
                fi
            fi
        else
            doEcho "${prog} is still NOT accessible; failing start"    
            stop
        fi
    else
        doEcho "Script to run ${prog}) (${exec}) does not exist"
        retval=5
    fi

    doEcho "Start result: $retval"
    return $retval
}

status() {
    isAccessible
}

stop() {
    doEcho "Stopping $prog..."
    kpid=`ps axf | grep "[j]ava .* -server .*${ADJURE_WILDFLY_BASE}" | awk '{print $1}'`
    if [ -n "$kpid" ] ; then
        let kwait=$shutdown_wait_iterations
        count=0

        # Try issuing SIGTERM
        kill -15 $kpid
        until [ `ps --pid $kpid 2> /dev/null | grep -c $kpid 2> /dev/null` -eq '0' ] || [ $count -gt $kwait ]
        do
          sleep 1
          let count=$count+1;
        done

        if [ $count -gt $kwait ]; then
          kill -9 $kpid
        fi
    fi
    retval=$?
    doEcho "Stop result: $retval"
    return $retval
}

case "$1" in
    start)
        #
        # If running (doesn't need to be accessible), then don't start again
        #
        isRunning
        retval=$?
        if [ $retval -eq 0 ]
        then
            doEcho "$prog already running"
            exit 0
        fi
        $1
        ;;
    stop)
        #
        # If running (doesn't need to be accessible), then stop
        #
        isRunning
        retval=$?
        if [ $retval -ne 0 ]
        then
            doEcho "$prog not running"
            exit 0
        fi
        $1
        ;;
    status)
        $1
        ;;
    *)
        usage
        exit 2
esac
exit $?
