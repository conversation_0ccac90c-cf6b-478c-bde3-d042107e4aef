#!/bin/bash

DIRNAME=`dirname "$0"`

. "$DIRNAME/deploy_common_ctrl"

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_WILDFLY_TOOLS

WILDFLY_CLI="$ADJURE_WILDFLY_HOME/bin/jboss-cli.sh -u=sysadmin -p=PassMe12#$"
DEPLOYMENT_NAME=$(adjure_wildfly_get_deployment_file_from_app_inst_ref_id $APP_INST_REF_ID)
DEPLOYMENT_FULL_PATH=$ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR/$DEPLOYMENT_NAME

# TODO: Put this in a file with better permissions
CONN_STUFF="--anyauth -u sysadmin:PassMe12#$"

case "$1" in
  start)
    preDeploy

    # Instead of everything below up to the exit, you could run this to use the jboss-cli (very slow)
    #$WILDFLY_CLI --connect --command="deploy $DEPLOYMENT_FULL_PATH --name=$DEPLOYMENT_NAME"

    doEcho "$DEPLOYMENT_NAME: Deploying archive: $DEPLOYMENT_FULL_PATH"
    bytes_value=$(curl -s -F "file=@$DEPLOYMENT_FULL_PATH" $CONN_STUFF http://localhost:9990/management/add-content 2>/dev/null | perl -pe 's/^.*"BYTES_VALUE"\s*:\s*"(.*)".*$/$1/' 2>/dev/null)
    json_string_start='{"content":[{"hash": {"BYTES_VALUE" : "'
    json_string_end='"}}], "address": [{"deployment":"'$DEPLOYMENT_NAME'"}], "operation":"add", "enabled":"true"}'
    json_string="$json_string_start$bytes_value$json_string_end"
    curl -s -S -H "Content-Type: application/json" -d "$json_string" $CONN_STUFF http://localhost:9990/management 2>/dev/null | perl -pe 's/^.*"outcome"\s*:\s*"(.*)".*$/$1/' >/dev/null 2>&1
    RESULT=$?
    ;;
  stop)
    $1
    RESULT=$?
    ;;
  status)
    $1
    RESULT=$?
    ;;
  *)
    usage
    RESULT=2
esac
exit $RESULT
