#!/bin/bash

DIRNAME=`dirname "$0"`

. "$DIRNAME/deploy_common_ctrl"

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_WILDFLY_TOOLS

WILDFLY_CLI="$ADJURE_WILDFLY_HOME/bin/jboss-cli.sh -u=sysadmin -p=PassMe12#$"
DEPLOYMENT_NAME=$(adjure_wildfly_get_deployment_file_from_app_inst_ref_id $APP_INST_REF_ID)
DEPLOYMENT_FULL_PATH=$ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR/$DEPLOYMENT_NAME

# TODO: Put this in a file with better permissions
CONN_STUFF="--anyauth -u sysadmin:PassMe12#$"

case "$1" in
  start)
    preDeploy

    EXPLODED_DEPLOYMENT_FULL_PATH=$ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR/exploded/$DEPLOYMENT_NAME
    doEcho "$DEPLOYMENT_NAME: Deploying exploded archive: $EXPLODED_DEPLOYMENT_FULL_PATH"
    # Explode the deployment archive into a "exploded" sub-directory from the main deployment file
    # Instead of everything below up to the exit, you could run this to use the jboss-cli (very slow)
    #$WILDFLY_CLI --connect "/deployment=$DEPLOYMENT_NAME/:add(runtime-name=$DEPLOYMENT_NAME, content=[{"path"=>"$EXPLODED_DEPLOYMENT_FULL_PATH", "archive"=>false}])"
    #$WILDFLY_CLI --connect "/deployment=$DEPLOYMENT_NAME/:deploy()"

    json_string='{"content":[{"path": "'$EXPLODED_DEPLOYMENT_FULL_PATH'", "archive": false}], "address": [{"deployment":"'$DEPLOYMENT_NAME'"}], "operation":"add"}'
    curl -s -S -H "Content-Type: application/json" -d "$json_string" $CONN_STUFF http://localhost:9990/management 2>/dev/null | perl -pe 's/^.*"outcome"\s*:\s*"(.*)".*$/$1/' >/dev/null 2>&1
    RESULT=$?
    if [[ "$RESULT" == "0" ]]; then
        curl -s -S -H "content-Type: application/json" -d '{"operation":"deploy", "address":[{"deployment":"'$DEPLOYMENT_NAME'"}]}' $CONN_STUFF http://localhost:9990/management 2>/dev/null | perl -pe 's/^.*"outcome"\s*:\s*"(.*)".*$/$1/' >/dev/null 2>&1
        RESULT=$?
    fi
    ;;
  stop)
    $1
    RESULT=$?
    ;;
  status)
    $1
    RESULT=$?
    ;;
  *)
    usage
    RESULT=2
esac
exit $RESULT
