
######################################################
# Begin: Session Server: Configuration
######################################################
#
# Session Server - Application Main Configuration
#
dn: uniqueIdentifier=%%UUID:sessionServerApplicationConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Session Server Application Configuration
objectClass: tgiConfigObject

#
# Session Server - Application Installation Main Configuration
#
dn: uniqueIdentifier=%%UUID:sessionServerInstallationConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Session Server Application Installation Configuration (parent)
objectClass: tgiConfigObject

#
# Session Server - Application Installation Service Configuration (Child of Main)
#
# service (Service) settings
#
dn: cn=Service,uniqueIdentifier=%%UUID:sessionServerInstallationConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Session Server Application Installation - Service Configuration (overrides)
objectClass: tgiConfigObject
subConfigRefId: uniqueIdentifier=%%UUID:javaDefaultConfig%%,ou=Config,dc=ticom-geo,dc=com

#
# Session Server - Application Installation Session Configuration (Child of Main)
#
# session service (Session) settings
#
dn: cn=Session,uniqueIdentifier=%%UUID:sessionServerInstallationConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Session Server Application Installation - Session Service Configuration (overrides)
objectClass: tgiConfigObject
subConfigRefId: uniqueIdentifier=%%UUID:javaDefaultConfig%%,ou=Config,dc=ticom-geo,dc=com

#
# Session Server - Application Installation Messaging Configuration (Child of Main)
#
# messaging (Messaging) settings
#
dn: cn=Messaging,uniqueIdentifier=%%UUID:sessionServerInstallationConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Session Server Application Installation - Messaging Configuration (overrides)
objectClass: tgiConfigObject
subConfigRefId: cn=Messaging,uniqueIdentifier=%%UUID:javaDefaultConfig%%,ou=Config,dc=ticom-geo,dc=com
configVar: {jms.provider}qpidjms

######################################################
# End: Session Server: Configuration
######################################################

######################################################
# Begin: Session Server: Startup Configuration Metadata
######################################################
#
# Session Server - Startup configuration metadata
#
dn: uniqueIdentifier=%%UUID:sessionServerStartup%%,ou=StartupConfig,dc=ticom-geo,dc=com
objectClass: tgiStartupObject
cn: Session Server Startup Config Metadata
startupRequested: TRUE
startupType: service
startupConfigRefId: uniqueIdentifier=%%UUID:wildflyServerStartupConfigForExplodedDeployments%%,ou=Config,dc=ticom-geo,dc=com
startupApplicationInstallationDependency: uniqueIdentifier=%%UUID:wildflyServerInstallation%%,ou=ApplicationInstallation,dc=ticom-geo,dc=com

######################################################
# End: Session Server: Startup Configuration Metadata
######################################################


######################################################
# Begin: Session Server: Application
######################################################
#
# Session Server - Application
#
dn: uniqueIdentifier=%%UUID:sessionServerApplication%%,ou=Application,dc=ticom-geo,dc=com
cn: Session Server Application
objectClass: tgiApplicationObject
applicationName: SessionServer
majorVersion: 1
minorVersion: 0
patchVersion: 0
configRefId: uniqueIdentifier=%%UUID:sessionServerApplicationConfig%%,ou=Config,dc=ticom-geo,dc=com

#
# Session Server - Application Installation
#
dn: uniqueIdentifier=%%UUID:sessionServerInstallation%%,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Session Server
objectClass: tgiApplicationInstallationObject
configRefId: uniqueIdentifier=%%UUID:sessionServerInstallationConfig%%,ou=Config,dc=ticom-geo,dc=com
busServiceRefId: uniqueIdentifier=%%UUID:sessionService_v1_1_0BusObj%%,ou=BusService,dc=ticom-geo,dc=com
applicationRefId: uniqueIdentifier=%%UUID:sessionServerApplication%%,ou=Application,dc=ticom-geo,dc=com
hostRefId: uniqueIdentifier=%%UUID:host%%,ou=Host,dc=ticom-geo,dc=com
startupRefId: uniqueIdentifier=%%UUID:sessionServerStartup%%,ou=StartupConfig,dc=ticom-geo,dc=com

######################################################
# End: Session Server: Application
######################################################

######################################################
# Begin: Session Server: Services
######################################################
#
# Session Service - BusService (Created/Owned by Session Server Application Installation)
#
dn: uniqueIdentifier=%%UUID:sessionService_v1_1_0BusObj%%,ou=BusService,dc=ticom-geo,dc=com
cn: Session Service BusService
objectclass: tgiBusServiceObject
interfaceName: tgi_session
majorVersion: 1
minorVersion: 1
patchVersion: 0
msgBusRefId: uniqueIdentifier=%%UUID:defaultMsgBus%%,ou=MessageBus,dc=ticom-geo,dc=com
serviceId: tgi_bus_session

######################################################
# End: Session Server: Services
######################################################

