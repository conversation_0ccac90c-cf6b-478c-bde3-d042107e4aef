#
# main.properties
#
messaging.jms.provider=qpidjms

####################
# Service settings
####################
#
# Status:       Optional
# Description:  The amount of time (in milliseconds) that a service should wait for a response to a request
# Default:      60000
#
#service.request.timeout=60000

####################
# Session settings
####################
#
# Status:       Optional
# Description:  The amount of time (in milliseconds) that a session should remain alive with no activity
# Default:      120000
#
#session.expire.timeout=120000

####################
# Classification settings
####################
#
# Status:       Optional
# Description:  The Classification colors can be overridden (different networks might use different colors for the same
#               classification)
# Default:      bg=#000000, fg=#FFFFFF
#
#classification.banner.background.color=#000000
#classification.banner.foreground.color=#FFFFFF