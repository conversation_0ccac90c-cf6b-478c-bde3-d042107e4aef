<?xml version="1.0" encoding="UTF-8"?>
<Configuration monitorInterval="30">
    <Properties>
        <Property name="MAIN_LAYOUT_PATTERN">%d{yyyy-MM-dd HH:mm:ss,SSS} %18.18tid:(%-35.35t) %-5p [%c] %m%n</Property>
        <Property name="log_root_dir">${env:ADJURE_LOG_ROOT}</Property>
        <Property name="deployment_name">session</Property>
    </Properties>
    <Appenders>
        <Console name="console">
            <PatternLayout pattern="${MAIN_LAYOUT_PATTERN}"/>
        </Console>
        <Async name="async_console" bufferSize="1000">
            <AppenderRef ref="console"/>
        </Async>

        <RollingFile name="main_file" fileName="${log_root_dir}/${deployment_name}/main.log"
                     filePattern="${log_root_dir}/archives/${deployment_name}-main-%d{yyyy-MM-dd-HH}-%i.log.gz">
            <PatternLayout pattern="${MAIN_LAYOUT_PATTERN}"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <TimeBasedTriggeringPolicy/>
            </Policies>
        </RollingFile>
        <Async name="async_main_file" bufferSize="1000">
            <AppenderRef ref="main_file"/>
        </Async>

        <RollingFile name="gpb_file" fileName="${log_root_dir}/${deployment_name}/gpb.log"
                     filePattern="${log_root_dir}/archives/${deployment_name}-gpb-%d{yyyy-MM-dd-HH}-%i.log.gz">
            <PatternLayout pattern="${MAIN_LAYOUT_PATTERN}"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <TimeBasedTriggeringPolicy/>
            </Policies>
        </RollingFile>
        <Async name="async_gpb_file" bufferSize="1000">
            <AppenderRef ref="gpb_file"/>
        </Async>

        <RollingFile name="messaging_file" fileName="${log_root_dir}/${deployment_name}/messaging.log"
                     filePattern="${log_root_dir}/archives/${deployment_name}-messaging-%d{yyyy-MM-dd-HH}-%i.log.gz">
            <PatternLayout pattern="${MAIN_LAYOUT_PATTERN}"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <TimeBasedTriggeringPolicy/>
            </Policies>
        </RollingFile>
        <Async name="async_messaging_file" bufferSize="1000">
            <AppenderRef ref="messaging_file"/>
        </Async>

        <RollingFile name="cfg_reg_file" fileName="${log_root_dir}/${deployment_name}/config_registry.log"
                     filePattern="${log_root_dir}/archives/${deployment_name}-config_registry-%d{yyyy-MM-dd-HH}-%i.log.gz">
            <PatternLayout pattern="${MAIN_LAYOUT_PATTERN}"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <TimeBasedTriggeringPolicy/>
            </Policies>
        </RollingFile>
        <Async name="async_cfg_reg_file" bufferSize="1000">
            <AppenderRef ref="cfg_reg_file"/>
        </Async>

    </Appenders>
    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="async_main_file"/>
        </Root>

        <Logger name="com.arjuna" level="WARN"/>
        <Logger name="org.apache.tomcat.util.modeler" level="WARN"/>
        <Logger name="sun.rmi" level="WARN"/>
        <Logger name="jacorb" level="WARN"/>
        <Logger name="jacorb.config" level="ERROR"/>
        <Logger name="com.ticomgeo" level="INFO"/>
        <Logger name="org.jboss.as.jpa" level="INFO"/>
        <Logger name="org.hibernate" level="INFO"/>
        <Logger name="org.jboss.weld.Bean" level="INFO"/>
        <Logger name="org.jboss.weld" level="INFO"/>

        <Logger name="com.ticomgeo.util.gpb.GpbUtils" additivity="false" level="DEBUG">
            <AppenderRef ref="async_gpb_file"/>
        </Logger>
        <Logger name="com.ticomgeo.jms.messaging.MessageHelpers" additivity="false" level="DEBUG">
            <AppenderRef ref="async_messaging_file"/>
        </Logger>
        <Logger name="ic.common.config" additivity="false" level="DEBUG">
            <AppenderRef ref="async_cfg_reg_file"/>
        </Logger>
        <Logger name="ic.common.impl.config" additivity="false" level="DEBUG">
            <AppenderRef ref="async_cfg_reg_file"/>
        </Logger>
    </Loggers>
</Configuration>
