#!/usr/bin/python
import uuid
import socket
import os.path
from optparse import OptionParser

def findReplacementValue(searchString, substitutions):
  newString = searchString
  startReplace = searchString.find("%%")
  if startReplace != -1:
    stopReplace = searchString.find("%%", startReplace+1)
    replaceString = searchString[startReplace: stopReplace+2]
    if replaceString in substitutions:
      newString = searchString.replace(replaceString, substitutions[replaceString])
    else:
      if replaceString.find("UUID") != -1:
        newValue = str(uuid.uuid1())
        substitutions[replaceString] = newValue
        newString = searchString.replace(replaceString, newValue)
  return newString
 
def populateSubstitutions(subs, subsFile):
  hostname = socket.getfqdn()
  subs["%%string:hostname%%"] = hostname
  for line in subsFile:
    entry = line.split("=")
    subs[entry[0].strip()] = entry[1].strip()

def updateSubstitutions(subs, subsFile):
  for key in subs:
    subsFile.write(key + " = " + subs[key] + "\n")

def main():
  parser = OptionParser()
  parser.add_option("-i","--input", help="Inaroptionsgsput ldif file to transform", type="string", dest="input")
  parser.add_option("-o","--output", help="Output ldif file", type="string", dest="output")
  parser.add_option("--msgBusHost", help="Hostname where the default bus is running.  Should only be invoked by the base installer", type="string", dest="msgBusHost")

  (options, args) = parser.parse_args()

  inputFile = str(options.input)
  outputFile = str(options.output)
  subsFile = "/h/adjure/base/config/ldifItems"
 
  input = open(inputFile, 'r')
  output = open(outputFile, 'w')
  if os.path.isfile(subsFile):
    subsFileIn = open(subsFile, 'r')
  else:
    subsFileIn = open(subsFile,'w+')

  substitutions = {}

  if options.msgBusHost:
    defaultMsgBusHostname = str(options.msgBusHost)
    substitutions["%%string:busHostName%%"] = defaultMsgBusHostname
  populateSubstitutions(substitutions, subsFileIn)

  subsFileIn.close()

  for line in input:
    output.write(findReplacementValue(line, substitutions))

  subsFileOut = open(subsFile, "w")
  updateSubstitutions(substitutions, subsFileOut)


if __name__ == "__main__": main()

