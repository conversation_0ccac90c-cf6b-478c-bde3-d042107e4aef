#!/bin/bash

if [[ "$#" != 1 || "$1" == "-h" || "$1" == "-help" ]]; then
  echo "Usage: $0 <ldif>"
  exit 1
fi

# pulling ADJURE_LOG_ROOT from here:
source /h/adjure/base/config/adjure_env.sh

ldapadd -H ${TGICONFIG_LDAP_URL} -D cn=admin,dc=ticom-geo,dc=com -w password -f $1 >> ${ADJURE_LOG_ROOT}/install.log
retval=$?
if [[ $retval != 0 ]]; then
    echo "Failed to add $1 to LDAP.  Error code $retval" |& tee -a ${ADJURE_LOG_ROOT}/install.log
fi
exit $retval