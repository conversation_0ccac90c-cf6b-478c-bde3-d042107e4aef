#!/bin/bash

ADJURE_BASEDIR=${ADJURE_BASEDIR:-/h/adjure/base}
ADJURE_CONFIG_ENV_FILE=$ADJURE_BASEDIR/config/adjure_env.sh
ADJURE_ENV_LOCK=/tmp/adjure_scripting_common_env.lock

# set_adjure_base_config <var> <value>
# (adds variable if it does not exist)
function set_adjure_base_config {
  {
    flock -x 200

    grep -q "^\s*export\s\s*$1=" $ADJURE_CONFIG_ENV_FILE
    if [[ $? -eq 0 ]]; then
      sed -i "s|^\(\s*export\s\s*$1=\).*$|\1\"$2\"|" $ADJURE_CONFIG_ENV_FILE
    else
      echo "export $1=\"$2\"" >> $ADJURE_CONFIG_ENV_FILE
    fi

    export $1="$2"
  } 200>$ADJURE_ENV_LOCK
  rm -f $ADJURE_ENV_LOCK
}

# get_adjure_base_config <var> <default>
function get_adjure_base_config {
  local result=
  {
    flock -x 200
    local line=$(grep "^\s*export\s\s*$1" $ADJURE_CONFIG_ENV_FILE 2>/dev/null)
    local ret_start=$(echo $line | sed "s|^\(\s*export\s\s*$1=\)\(.*\)$|\2|")
    local ret=$(echo $ret_start | sed "s|^\"\(.*\)\"$|\1|")
    if [[ "$ret" == "" ]]; then
      echo -n $2
    else
      echo -n $ret
    fi
  } 200>$ADJURE_ENV_LOCK
  rm -f $ADJURE_ENV_LOCK

  echo $result
}

# remove_adjure_base_config <var>
function remove_adjure_base_config {
  {
    flock -x 200
    grep -v "^\s*export\s\s*$1=" $ADJURE_CONFIG_ENV_FILE > $ADJURE_CONFIG_ENV_FILE.tmp
    cp -a $ADJURE_CONFIG_ENV_FILE.tmp $ADJURE_CONFIG_ENV_FILE
    rm $ADJURE_CONFIG_ENV_FILE.tmp
  } 200>$ADJURE_ENV_LOCK
  rm -f $ADJURE_ENV_LOCK
}

# logged_unzip <args to unzip>
function logged_unzip {
    source ${ADJURE_CONFIG_ENV_FILE}
    unzip $@ &>> ${ADJURE_LOG_ROOT}/install.log
}

# returns true if during install the installer requested an upgrade instead of a fresh install
function is_upgrade {
    if [ ! -f /etc/sysconfig/adjure_preserve ] ; then
        return 1
    fi

    source /etc/sysconfig/adjure_preserve
    test "${USE_PRESERVED_INSTALL}" == "Y"
}
