#!/bin/bash

ADJURE_BASEDIR=/h/adjure/base/
ADJURE_CONFIG_DIR=${ADJURE_BASEDIR}/config
ADJURE_LDIF_ITEMS=${ADJURE_CONFIG_DIR}/ldifItems
ADJURE_STARTUP_CONFIG=/etc/sysconfig/adjure

source ${ADJURE_CONFIG_DIR}/adjure_env.sh

hostnamePort=${DEFAULT_MSGBUS_URL#amqp://}
hostname=${hostnamePort%:[0-9]*}

python ${ADJURE_BASEDIR}/bin/fill_ldif_template.py -i ${ADJURE_CONFIG_DIR}/base_config_host_unique.ldif.template -o ${ADJURE_CONFIG_DIR}/base_config_host_unique.ldif --msgBusHost ${hostname} >/dev/null
python ${ADJURE_BASEDIR}/bin/fill_ldif_template.py -i ${ADJURE_CONFIG_DIR}/qpid-broker.ldif.template -o ${ADJURE_CONFIG_DIR}/qpid-broker.ldif >/dev/null

if [ "$TGICONFIG_TYPE" == "LDAP" ]; then

  if [ -z ${TGICONFIG_LDAP_URL+x} ]; then
    echo "LDAPSERVER was not assigned in ${ADJURE_CONFIG_DIR}/adjure_config.sh so no config was pushed to any LDAP server"
  else
    echo "Pushing configuration for this host to the ldap server located at $TGICONFIG_LDAP_URL" >> ${ADJURE_LOG_ROOT}/install.log

    for ldif in base_config_organization.ldif base_config_host_unique.ldif qpid-broker.ldif; do
      echo "loading ldif from " ${CONFIG_DIR}/${ldif} >> ${ADJURE_LOG_ROOT}/install.log
      ${ADJURE_BASEDIR}/bin/add_ldif_to_ldap.sh ${ADJURE_CONFIG_DIR}/${ldif} |& tee -a ${ADJURE_LOG_ROOT}/install.log
    done

  fi
fi

line=$(grep "%%UUID:startupService%%" $ADJURE_LDIF_ITEMS 2>/dev/null)
startupAppUUID=$(echo $line | sed "s|%%UUID:startupService%%\s*=\s*||")

if [ -f $ADJURE_STARTUP_CONFIG ]; then
  if [ "$(grep "STARTUP_APP_REF_ID" $ADJURE_STARTUP_CONFIG 2>/dev/null)" != "" ]; then
    sed -i "s|^\(\s*export\s*STARTUP_APP_REF_ID\s*=\s*\).*|\1$startupAppUUID|" $ADJURE_STARTUP_CONFIG
  else
    echo "export STARTUP_APP_REF_ID=$startupAppUUID" >> $ADJURE_STARTUP_CONFIG
  fi
else
  echo "export STARTUP_APP_REF_ID=$startupAppUUID" >> $ADJURE_STARTUP_CONFIG
fi
chmod 0775 $ADJURE_STARTUP_CONFIG

echo "Configuration complete"

