#!/bin/bash
ADJURE_LDIF_ITEMS=/h/adjure/base/config/ldifItems
ADJURE_STARTUP_CONFIG=/etc/sysconfig/adjure

source /h/adjure/base/config/adjure_env.sh

hostnamePort=${DEFAULT_MSGBUS_URL#amqp://}
hostname=${hostnamePort%:[0-9]*}

errCounter=1

function check-for-error {
  errCode=$?
  if [ $errCode != "0" ]; then 
    errorInfo[$errCounter]="Failed to add $1 to LDAP.  Error code $errCode"
    ((errCounter +=1))
  fi 
}


python /h/adjure/fill_ldif_template.py -i /h/adjure/base/config/base_config_host_unique.ldif.template -o /h/adjure/base/config/base_config_host_unique.ldif --msgBusHost ${hostname} >/dev/null

if [ -d /h/adjure/base/config/ ]; then
  for f in $(ls /h/adjure/base/config/*template); do
    python /h/adjure/base/bin/fill_ldif_template.py -i $f -o ${f%\.template} >/dev/null
  done;
fi
if [ -d /h/adjure/base/config/ ]; then
  for f in $(ls /h/adjure/base/config/*template); do
    python /h/adjure/base/bin/fill_ldif_template.py -i $f -o ${f%\.template} >/dev/null
  done;
fi
if [ -d /h/adjure/base/config/ ]; then
  for f in $(ls /h/adjure/base/config/*template); do
    python /h/adjure/base/bin/fill_ldif_template.py -i $f -o ${f%\.template} >/dev/null
  done;
fi
if [ -d /h/adjure/base/config/ ]; then
  for f in $(ls /h/adjure/base/config/*template); do
    python /h/adjure/base/bin/fill_ldif_template.py -i $f -o ${f%\.template} >/dev/null
  done;
fi

if [ "$TGICONFIG_TYPE" == "LDAP" ]; then

  if [ -z ${TGICONFIG_LDAP_URL+x} ]; then
    echo "LDAPSERVER was not assigned in /h/adjure/base/adjure_env.sh so no config was pushed to any LDAP server"
  else
    echo "Pushing configuration for this host to the ldap server located at $TGICONFIG_LDAP_URL"
    ldapadd -H ${TGICONFIG_LDAP_URL} -D cn=admin,dc=ticom-geo,dc=com -w password -f /h/adjure/base/config/base_config_organization.ldif >/dev/null 2>&1
    ldapadd -H ${TGICONFIG_LDAP_URL} -D cn=admin,dc=ticom-geo,dc=com -w password -f /h/adjure/base/config/base_config_host_unique.ldif >/dev/null 2>&1
    check-for-error "/h/adjure/base/config/0/base_config_host_unique.ldif"
    if [ -d /h/adjure/base/config/ ]; then
      for f in $(ls /h/adjure/base/config/*ldif); do
        ldapadd -H ${TGICONFIG_LDAP_URL} -D cn=admin,dc=ticom-geo,dc=com -w password -f $f >/dev/null 2>&1
        check-for-error $f 
      done;
    fi
    if [ -d /h/adjure/base/config/ ]; then
      for f in $(ls /h/adjure/base/config/*ldif); do
        ldapadd -H ${TGICONFIG_LDAP_URL} -D cn=admin,dc=ticom-geo,dc=com -w password -f $f >/dev/null 2>&1
        check-for-error $f 
      done;
    fi
    if [ -d /h/adjure/base/config/ ]; then
      for f in $(ls /h/adjure/base/config/*ldif); do
        ldapadd -H ${TGICONFIG_LDAP_URL} -D cn=admin,dc=ticom-geo,dc=com -w password -f $f >/dev/null 2>&1
        check-for-error $f 
      done;
    fi

    if [ -d /h/adjure/base/config/ ]; then
      for f in $(ls /h/adjure/base/config/*ldif); do
        ldapadd -H ${TGICONFIG_LDAP_URL} -D cn=admin,dc=ticom-geo,dc=com -w password -f $f >/dev/null 2>&1
        check-for-error $f 
      done;
    fi
  fi
fi

line=$(grep "%%UUID:startupService%%" $ADJURE_LDIF_ITEMS 2>/dev/null)
startupAppUUID=$(echo $line | sed "s|%%UUID:startupService%%\s*=\s*||")

if [ -f $ADJURE_STARTUP_CONFIG ]; then
  if [ "$(grep "STARTUP_APP_REF_ID" $ADJURE_STARTUP_CONFIG 2>/dev/null)" != "" ]; then
    sed -i "s|^\(\s*export\s*STARTUP_APP_REF_ID\s*=\s*\).*|\1$startupAppUUID|" $ADJURE_STARTUP_CONFIG
  else
    echo "export STARTUP_APP_REF_ID=$startupAppUUID" >> $ADJURE_STARTUP_CONFIG
  fi
else
  echo "export STARTUP_APP_REF_ID=$startupAppUUID" >> $ADJURE_STARTUP_CONFIG
fi

echo "Configuration complete"

if [ ${#errorInfo[@]} != "0" ]; then
  for i in `seq 1 ${#errorInfo[@]}`; do
  echo "${errorInfo[$i]}"
 done;
fi 
