#!/bin/bash

JBOSS_BASE=/h/jboss/tgi-wildfly/standalone/deployments/
STAGING=/h/tags/jboss_staging

function usage() {
  echo $"Usage: $0 {deploy|status|undeploy} <base war or ear file basename> <property_path> <app_uuid>"
  echo "   Status return code 1 means try again later. 2 means failed, 0 means success."
}

if [ "$2" = "" ]; then
  usage
  exit 2
fi

case "$1" in
  deploy)
    rm -f $JBOSS_BASE/$2.*
    if [ "$3" = "" ]; then 
      usage
      exit 2
    fi
    sed -i "s|^\(\s*ldap.application.installationid\s*=\s*\).*|\1$4|" $3 
    cp -f $STAGING/$2 $JBOSS_BASE
    exit $?
    ;;
  undeploy)
    rm -f $JBOSS_BASE/$2
    exit $?
    ;;
  status)
    stat $JBOSS_BASE/$2 > /dev/null 2>&1
    if [ "$?" != "0" ]; then exit 2; fi
    stat $JBOSS_BASE/$2.deployed > /dev/null 2>&1
    if [ "$?" = "0" ]; then exit 0; fi
    stat $JBOSS_BASE/$2.failed > /dev/null 2>&1
    if [ "$?" = "0" ]; then exit 2; fi
    exit 1
    ;;
  *)
    usage
    exit 2
esac

exit 0
