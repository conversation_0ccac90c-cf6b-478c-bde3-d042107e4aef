#!/usr/bin/python

import os.path
from optparse import OptionParser


def populateSubstitutions(subs, subsFile):
    for line in subsFile:
        entry = line.split("=")
        subs[entry[0].strip()] = entry[1].strip()


def main():
    parser = OptionParser()
    parser.add_option("-f", "--find", help="The pattern to find in the replacement file", type="string", dest="find")

    (options, args) = parser.parse_args()

    toFind = str(options.find)
    subsFile = "/h/adjure/base/config/ldifItems"

    substitutions = {}

    if os.path.isfile(subsFile):
        subsFileIn = open(subsFile, 'r')
        populateSubstitutions(substitutions, subsFileIn)
        subsFileIn.close()

    if toFind in substitutions:
        print substitutions[toFind]
        exit(0)
    else:
        exit(1)


if __name__ == "__main__": main()
