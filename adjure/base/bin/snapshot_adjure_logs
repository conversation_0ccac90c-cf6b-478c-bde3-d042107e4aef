#!/bin/bash
uuid=$(uuidgen)
baseFolder=~/
tempFolder=adjureLogSnapShot$uuid

if [ "$1" == "-h" ]; then
  echo "Copies logs associated with adjure and j42-adapter logs if they exist. It dumps the ldap config and tars all of them into the  output file." 
  echo "USAGE: snapshot_adjure_logs <optional file name>"
  exit 0
fi

mkdir $baseFolder/$tempFolder
cp -r /var/log/adjure/ $baseFolder/$tempFolder
if [ -d /h/tgi/j42-trs-adapter/log/ ]; then
	mkdir $baseFolder/$tempFolder/j42-trs-adapter
	cp -r /h/tgi/j42-trs-adapter/log/*log* $baseFolder/$tempFolder/j42-trs-adapter/
fi
mkdir $baseFolder/$tempFolder/ldap
source /h/adjure/base/config/adjure_env.sh
/h/adjure/base/bin/dump_ldap -H $TGICONFIG_LDAP_URL > $baseFolder/$tempFolder/ldap/raw_ldap
tarFileName=adjureLogs
if [ "$1" != "" ]; then
	tarFileName=$1
fi
tar cvzf $tarFileName.tar.gz -C $baseFolder/ $tempFolder/
rm -rf $baseFolder/$tempFolder
echo "Logs compressed into $tarFileName.tar.gz"
