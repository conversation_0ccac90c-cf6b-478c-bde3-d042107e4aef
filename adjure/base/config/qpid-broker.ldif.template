
######################################################
# Adjure Qpid Broker
######################################################
#
# Adjure Qpid Broker - Application
#
dn: uniqueIdentifier=%%UUID:qpidBrokerApplication%%,ou=Application,dc=ticom-geo,dc=com
cn: Qpid Broker Application
objectClass: tgiApplicationObject
applicationName: qpidBroker
majorVersion: 1
minorVersion: 0
patchVersion: 0

#
# Adjure Qpid Broker - Application Installation
#
dn: uniqueIdentifier=%%UUID:qpidBrokerInstallation%%,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Qpid Broker
objectClass: tgiApplicationInstallationObject
hostRefId: uniqueIdentifier=%%UUID:host%%,ou=Host,dc=ticom-geo,dc=com
startupRefId: uniqueIdentifier=%%UUID:qpidBrokerStartup%%,ou=StartupConfig,dc=ticom-geo,dc=com

#
# Adjure Qpid Broker - Startup Config
#
dn: uniqueIdentifier=%%UUID:qpidBrokerStartup%%,ou=StartupConfig,dc=ticom-geo,dc=com
objectClass: tgiStartupObject
startupRequested: TRUE
startupType: service
startupConfigRefId: uniqueIdentifier=%%UUID:qpidBrokerStartupConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Qpid Broker Startup Config Metadata

#
# Adjure Qpid Broker - Startup configuration settings
#
dn: uniqueIdentifier=%%UUID:qpidBrokerStartupConfig%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {serviceScriptPath}/h/adjure/base/qpid-broker/qpid_svc.sh
configVar: {workingDirectory}/h/adjure/base/qpid-broker
configVar: {unknownStatusReturnCode}99
cn: Qpid Broker Startup Config Settings

