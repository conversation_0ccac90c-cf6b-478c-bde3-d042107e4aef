#!/bin/bash

if [ -n "${INSTALL_PRESERVE_DIR}" ] ; then
    script_path=$(dirname $(readlink -f $0))
    echo "Backing up LDAP configuration to ${INSTALL_PRESERVE_DIR}"
    echo "TGICONFIG_LDAP_URL=$TGICONFIG_LDAP_URL"> $INSTALL_PRESERVE_DIR/ldap.properties

    echo "Backing up Classification configuration to ${INSTALL_PRESERVE_DIR}"
    cp $script_path/class_persist.config $INSTALL_PRESERVE_DIR/class_persist.config
fi
