export TGICONFIG_TYPE=LDAP
export TGICONFIG_LDAP_URL=ldap://localhost:389
export GEOVIEWER_MSGBUS_URL=amqp://localhost:5672
export DEFAULT_MSGBUS_URL="amqp://localhost.localdomain:5672"
export ADJURE_BASEDIR="/h/adjure/base"
export ADJURE_SCRIPTING_COMMON="/h/adjure/base/bin/adjure-scripting-common"
export ADJURE_FILL_LDIF_TEMPLATE="/h/adjure/base/bin/fill_ldif_template.py"
export ADJURE_ADD_LDIF="/h/adjure/base/bin/add_ldif_to_ldap.sh"
export ADJURE_RETRIEVE_LDIF_ITEM="/h/adjure/base/bin/retrieve_ldif_subst.py"
export ADJURE_POSTINSTALL_SCRIPT="/h/adjure/base/config/adjure_postinstall.sh"
export ADJURE_PREUNINSTALL_SCRIPT="/h/adjure/base/config/adjure_preuninstall.sh"
export ADJURE_FIX_CLASSIFICATION_PROPERTIES="/h/adjure/base/config/adjure_fix_classification_properties.sh"
export ADJURE_LOG_ROOT="/var/log_adjure"
export ADJURE_SERVICE_ACCESS_PORT="10040"
export ADJURE_SERVICE_DEBUG="false"
export ADJURE_SERVICE_DIR="/h/adjure/base/adjure-service"
export ADJURE_WILDFLY_BASE="/h/adjure-jboss"
export ADJURE_WILDFLY_HOME="/h/adjure-jboss/wildfly"
export ADJURE_WILDFLY_BIN_DIR="/h/adjure-jboss/wildfly/bin"
export ADJURE_WILDFLY_CLI="/h/adjure-jboss/wildfly/bin/jboss-cli.sh --connect -u=sysadmin -p=PassMe12#$"
export ADJURE_WILDFLY_DATADIR="/var/opt/adjure-jboss/wildfly-21.0.2.Final/standalone"
export ADJURE_WILDFLY_DEPLOYMENT_STAGING_DIR="/h/adjure-jboss/deployments"
export ADJURE_WILDFLY_TOOLS_PATH="/h/adjure-jboss/adjure-extras/adjure-tools"
export ADJURE_WILDFLY_TOOLS="/h/adjure-jboss/adjure-extras/adjure-tools/adjure_wildfly_tools.sh"
export ADJURE_WILDFLY_RECONFIGURE_TOOLS="/h/adjure-jboss/adjure-extras/adjure-tools/adjure_wildfly_reconfigure_tools.sh"
export ADJURE_WILDFLY_MODULES_PATH="/h/adjure-jboss/wildfly/adjure-modules"
export ADJURE_WILDFLY_ADD_NATIVE_LIBS_DIR="/h/adjure-jboss/adjure-extras/adjure-tools/add_native_libs_dir.sh"
export ADJURE_WILDFLY_REMOVE_NATIVE_LIBS_DIR="/h/adjure-jboss/adjure-extras/adjure-tools/remove_native_libs_dir.sh"
export ADJURE_WILDFLY_INSTALL_CONFIG_CERT_STORE_PWD_EXT="/h/adjure-jboss/adjure-extras/adjure-tools/install-config-cert-store-pwd-ext.sh"
export ADJURE_WILDFLY_UNINSTALL_CONFIG_CERT_STORE_PWD_EXT="/h/adjure-jboss/adjure-extras/adjure-tools/uninstall-config-cert-store-pwd-ext.sh"
export ADJURE_WILDFLY_LOWEST_MAX_MEM_MEG="3584"
export ADJURE_WILDFLY_EJB_THREAD_COUNT="25"
export WILDFLY_J42_OVERRIDE_FILE="/var/opt/adjure-jboss/wildfly-21.0.2.Final/standalone/configuration/j42_override.properties"
export WILDFLY_KEYSTORE_PWD="secret"
export WILDFLY_TRUSTSTORE_PWD="secret"
export SESSIONSERVER_APPINSTALL_UUID="cd4490e2-ee3f-11eb-bd4e-1f03d69ea65b"
export deploymentfile_cd4490e2_ee3f_11eb_bd4e_1f03d69ea65b="adjure-session.war"
export OOMADASHBOARDSERVER_APPINSTALL_UUID="ce42e048-ee3f-11eb-8d6a-f9f38488b7f3"
export deploymentfile_ce42e048_ee3f_11eb_8d6a_f9f38488b7f3="dashboard-server-war-2.1.8.war"
export pre_deploy_script_ce42e048_ee3f_11eb_8d6a_f9f38488b7f3="/h/dashboard/dashboard-server/pre_deploy.sh"
export post_undeploy_script_ce42e048_ee3f_11eb_8d6a_f9f38488b7f3="/h/dashboard/dashboard-server/post_undeploy.sh"
export OOMADASHBOARDWEBCLIENT_APPINSTALL_UUID="cf37a808-ee3f-11eb-a9d7-17c1b2ce6533"
export deploymentfile_cf37a808_ee3f_11eb_a9d7_17c1b2ce6533="dashboard-webclient-war-2.1.8.war"
export ADJURE_CLASSIFICATION_INFO="/h/adjure/base/config/class_persist.config"
export ADJURE_LOCAL_HOST="localhost.localdomain"
export WILDFLY_HTTP_PORT="8080"
export WILDFLY_HTTPS_PORT="8443"
export WILDFLY_USE_SSL="y"
export WILDFLY_USE_CASPORT="n"
export ADJURE_WILDFLY_SECMGR="false"
export WILDFLY_SHIRO_LAST_PKI_MODE="--pki"
export WILDFLY_SHIRO_CURRENT_MODE="--pki"
export ADJURE_WILDFLY_SYSTEM_PROPS="-Dcore.envelope.useWoodstox=false"
