
######################################################
# Adjure Qpid Broker
######################################################
#
# Adjure Qpid Broker - Application
#
dn: uniqueIdentifier=b51f6758-ee3f-11eb-93c6-edd4a0bf3b9a,ou=Application,dc=ticom-geo,dc=com
cn: Qpid Broker Application
objectClass: tgiApplicationObject
applicationName: qpidBroker
majorVersion: 1
minorVersion: 0
patchVersion: 0

#
# Adjure Qpid Broker - Application Installation
#
dn: uniqueIdentifier=b51f6b04-ee3f-11eb-93c6-edd4a0bf3b9a,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Qpid Broker
objectClass: tgiApplicationInstallationObject
hostRefId: uniqueIdentifier=b51245d2-ee3f-11eb-ad7a-57820c39941d,ou=Host,dc=ticom-geo,dc=com
startupRefId: uniqueIdentifier=b51f6db6-ee3f-11eb-93c6-edd4a0bf3b9a,ou=StartupConfig,dc=ticom-geo,dc=com

#
# Adjure Qpid Broker - Startup Config
#
dn: uniqueIdentifier=b51f6db6-ee3f-11eb-93c6-edd4a0bf3b9a,ou=StartupConfig,dc=ticom-geo,dc=com
objectClass: tgiStartupObject
startupRequested: TRUE
startupType: service
startupConfigRefId: uniqueIdentifier=b51f705e-ee3f-11eb-93c6-edd4a0bf3b9a,ou=Config,dc=ticom-geo,dc=com
cn: Qpid Broker Startup Config Metadata

#
# Adjure Qpid Broker - Startup configuration settings
#
dn: uniqueIdentifier=b51f705e-ee3f-11eb-93c6-edd4a0bf3b9a,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {serviceScriptPath}/h/adjure/base/qpid-broker/qpid_svc.sh
configVar: {workingDirectory}/h/adjure/base/qpid-broker
configVar: {unknownStatusReturnCode}99
cn: Qpid Broker Startup Config Settings

