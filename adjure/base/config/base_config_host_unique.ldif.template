########
# Data
########

#
# host name
#
dn: uniqueIdentifier=%%UUID:host%%,ou=Host,dc=ticom-geo,dc=com
objectclass: tgiHostObject
hostName: %%string:hostname%%
classificationRefId: uniqueIdentifier=%%UUID:defaultClassification%%,ou=Classification,dc=ticom-geo,dc=com

# TODO: Not done converting things over to startup
dn: uniqueIdentifier=%%UUID:disabledStartup%%,ou=StartupConfig,dc=ticom-geo,dc=com
objectClass: tgiStartupObject
startupType: disabled
startupRequested: FALSE
startupConfigRefId: uniqueIdentifier=%%UUID:disabledStartupConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: dummy startup object for things that haven't been converted yet

# config for disabled startup
dn: uniqueIdentifier=%%UUID:disabledStartupConfig%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: dummy config for a disabled startup

#
# default msg bus
#
dn: uniqueIdentifier=%%UUID:defaultMsgBus%%,ou=MessageBus,dc=ticom-geo,dc=com
cn: default_msg_bus
objectclass: tgiMessageBusObject
preferredBrokerName: anonymous_qpid
preferredBrokerConfigRefId: uniqueIdentifier=%%UUID:preferredBrokerConfig%%,ou=Config,dc=ticom-geo,dc=com

#
#default msg bus config
#
dn: uniqueIdentifier=%%UUID:preferredBrokerConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: default localhost msgbus guest config
configVar: {wireProtocol}amqp
configVar: {transport}tcp
configVar: {host}%%string:busHostName%%
configVar: {port}5672
configVar: {cmdNodeName}amq.topic
configVar: {eventNodeName}amq.topic
configVar: {publishreplyNodeName}amq.topic
objectClass: tgiConfigObject

#
# default logger object
#
dn: uniqueIdentifier=%%UUID:defaultLogger%%,ou=Logger, dc=ticom-geo,dc=com
loggerConfigRefId: uniqueIdentifier=%%UUID:defaultLoggerConfig%%,ou=Config,dc=ticom-geo,dc=com
loggerName: log4cxx
objectClass: tgiLoggerObject
cn: default logger (logs to console)

#
# default logger configuraton
#
dn: uniqueIdentifier=%%UUID:defaultLoggerConfig%%,ou=Config, dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {log4j.rootLogger}DEBUG, console
configVar: {log4j.appender.console}org.apache.log4j.ConsoleAppender
configVar: {log4j.appender.console.layout}org.apache.log4j.PatternLayout
configVar: {log4j.appender.console.layout.ConversionPattern}%d{ISO8601}{GMT} %-5p %-25c %30.30l - %m%n

#
# Startup Service
#
dn: uniqueIdentifier=%%UUID:startupService%%,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Startup Service
objectClass: tgiApplicationInstallationObject
configRefId: uniqueIdentifier=%%UUID:startupServiceConfig%%,ou=Config,dc=ticom-geo,dc=com
hostRefId: uniqueIdentifier=%%UUID:host%%,ou=Host,dc=ticom-geo,dc=com
startupRefId: uniqueIdentifier=%%UUID:disabledStartup%%,ou=StartupConfig,dc=ticom-geo,dc=com
loggerInfoRefId: uniqueIdentifier=%%UUID:startupServiceLogger%%,ou=Logger,dc=ticom-geo,dc=com

dn: uniqueIdentifier=%%UUID:startupServiceConfig%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: Startup Service Config
configVar: {workIntervalSec}3
configVar: {statusSocket}/tmp/adjure_startup.sock

# Startup Service (C++)
dn: uniqueIdentifier=%%UUID:startupServiceLogger%%,ou=Logger,dc=ticom-geo,dc=com
loggerConfigRefId: uniqueIdentifier=%%UUID:startupServiceLoggerConfig%%,ou=Config,dc=ticom-geo,dc=com
loggerName: log4cxx
objectClass: tgiLoggerObject

dn: uniqueIdentifier=%%UUID:startupServiceLoggerConfig%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {log4j.rootLogger}DEBUG, file
configVar: {log4j.appender.file}org.apache.log4j.RollingFileAppender
configVar: {log4j.appender.file.file}${ADJURE_LOG_ROOT}/adjure_startup/adjure_startup.log
configVar: {log4j.appender.file.layout}org.apache.log4j.PatternLayout
configVar: {log4j.appender.file.layout.ConversionPattern}%d{ISO8601}{GMT} %-5p %-25c %30.30l - %m%n
configVar: {log4j.appender.file.MaxFileSize}20MB
configVar: {log4j.appender.file.MaxBackupIndex}100

#########################################
# Adjure-Java application common configuration objects
#########################################
#
# Shared configuration settings - Main Configuration
#
dn: uniqueIdentifier=%%UUID:javaDefaultConfig%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: Common configuration

#
# service request timeout settings
#
dn: cn=Service,uniqueIdentifier=%%UUID:javaDefaultConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Common service request configuration
objectClass: tgiConfigObject
# deprecated
configVar: {request.timeout}60000

#
# session service (Session) settings
#
dn: cn=Session,uniqueIdentifier=%%UUID:javaDefaultConfig%%,ou=Config,dc=ticom-geo,dc=com
cn: Common Session service configuration
objectClass: tgiConfigObject
configVar: {expire.timeout}240000

#
# messaging settings
#
dn: cn=Messaging,uniqueIdentifier=%%UUID:javaDefaultConfig%%,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: Common messaging configuration
configVar: {jms.provider}qpidjms
configVar: {qpid.jms.connection.username}guest
configVar: {qpid.jms.connection.password}guest
configVar: {request.timeoutms}60000

