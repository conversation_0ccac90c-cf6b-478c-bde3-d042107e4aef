#!/bin/bash

if [[ "$1" == "" ]]; then
  echo "Usage: $0 <classification_properties_file>"
  exit 1
fi

file=$1

source /h/adjure/base/config/adjure_env.sh
source $ADJURE_CLASSIFICATION_INFO

class_level=$ADJURE_BASE_CLASS_LEVEL
echo "Correcting $file classification based on level=\"$class_level\", compartment=(${ADJURE_BASE_CLASS_COMPART[*]}), releaseto=(${ADJURE_BASE_CLASS_RELEASETO[*]})"
if [[ ! -r $file ]]; then
  echo "Permissions problem (read) for classification file $file. It should be manually changed."
  exit 1
fi

if [[ ! -w $file ]]; then
  echo "Permissions problem (write) for classification file $file. It should be manually changed."
  exit 1
fi

function my_join() {
  local IFS=,
  echo "$*"
}

RET=0
my_file_subst() {
  local f=$1
  shift

  local key=$1
  shift

  local value=("${@}")

  # Check if already set
  if grep -q -P "^\s*$key\s*=" $f; then
    echo "WARNING! saw classification.properties file that already contains an uncommented $key"
    echo "which is not correct. It should manually be changed."
    echo "Note - file was: $f"
    RET=1
  fi
  
  # Verify the placeholder is present
  if grep -q -P "^\s*#$key\s*=" $f; then
    if [[ ! -e $f.orig ]]; then cp -pf $f $f.orig; fi
    if [[ ${#value[@]} != 0 ]]; then
      replacement="$key=$(my_join ${value[@]})"
    else
      return
    fi

    sed -i $f -e "s/^\s*#$key\s*=.*/$replacement/"
    if [[ "$?" != "0" ]]; then
      echo "WARNING! Failed to update $key"
      echo "Note - file was: $f"
      RET=1
    fi
    sed -i $f -e 's/\\n'$key'/\n'$key'/g'
    if [[ "$?" != "0" ]]; then
      echo "WARNING! Problem fixing newlines, file may be corrupt"
      echo "Note - file was: $f"
      RET=1
    fi
  else
    echo "No placeholder $key present. File should be manually changed."
    echo "Note - file was: $f"
    RET=1
  fi
}

my_file_subst $file System.Level $ADJURE_BASE_CLASS_LEVEL
my_file_subst $file System.Compartment ${ADJURE_BASE_CLASS_COMPART[@]}
my_file_subst $file System.ReleaseTo ${ADJURE_BASE_CLASS_RELEASETO[@]}

exit $RET
