dn: dc=ticom-geo,dc=com
o: Ticom Geomatics, Inc
objectClass: dcObject
objectClass: organization
description: base domain object for ticom-geo.com

########
# Top-level hierarchy
########
dn: ou=People,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for information about people

dn: ou=Classification,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for classification information referred to by other entities, typically hosts.

dn: ou=MessageBus,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for message bus connection configuration info

dn: ou=Config,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for configuration information

dn: ou=Application,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for application information, common across installations of that application

dn: ou=Host,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for host information

dn: ou=ApplicationInstallation,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for application installation information, specific to a particular installation/instance

dn: ou=StartupConfig,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for startup configuration

dn: ou=BusService,dc=ticom-geo,dc=com
objectClass: organizationalUnit
description: Subtree for bus service configuration

dn: ou=Logger, dc=ticom-geo,dc=com
ou: Logger
description: Subtree for logger configuration
objectClass: organizationalUnit

