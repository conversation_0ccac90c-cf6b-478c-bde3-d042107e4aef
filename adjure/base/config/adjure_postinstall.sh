#!/bin/bash
#
# This script is meant to be called  by an iso's postinstall script, or after install of the base RPM
# If called after base RPM installation, it will set up classification levels in LDAP.
# If called after all RPMs are installed (in ISO post install), it will also correct property files
# with the classification information it has
#

if [[ "$(whoami)" != "root" ]]; then
  echo "Must be root"
  exit 1
fi

script_path=$(dirname $(readlink -f $0))

# Note: returning 0 means "true" since this goes in an if/while statement (i.e. 0 == invalid, 1 == valid)
invalid_classlevel() {
  if [[ "$1" != "U" ]] && [[ "$1" != "C" ]] && [[ "$1" != "S" ]] && [[ "$1" != "TS" ]]; then
    return 0
  fi
  return 1
}

# Note: returning 0 means "true" since this goes in an if/while statement (i.e. 0 == invalid, 1 == valid)
invalid_compartment() {
  # An all-empty compartment is actually valid.
  if [[ "$#" == "0" ]]; then
    return 1
  fi

  # This loop stops when it knows the answer. We can only know the answer if we see an invalid one
  for c in $*; do
    if [[ "$c" != "SI" ]] && [[ "$c" != "TK" ]]; then
      return 0
    fi
  done

  # If we got here, then they were all valid.
  return 1
}

# Note: returning 0 means "true" since this goes in an if/while statement (i.e. 0 == invalid, 1 == valid)
invalid_releaseto() {
  # An all-empty releaseto is actually valid.
  if [[ "$#" == "0" ]]; then
    return 1
  fi

  # This loop stops when it knows the answer. We can only know the answer if we see an invalid one
  for r in $*; do
    if [[ "$r" != "FOUO" ]] && [[ "$r" != "ROK" ]] && [[ "$r" != "EY3" ]] && [[ "$r" != "EY4" ]] && \
       [[ "$r" != "EY5" ]] && [[ "$r" != "EY9" ]] && [[ "$r" != "NOFOR" ]]; then
      return 0
    fi
  done

  # If we got here, then they were all valid.
  return 1
}

# Store classification for other things. We just store the class_persist file which others need to
# source, since it holds arrays.
source /h/adjure/base/config/adjure_env.sh
source $ADJURE_SCRIPTING_COMMON

source $script_path/class_persist.config
if [[ "$?" != "0" ]]; then
  echo "Script must be executed from its original location (should be /h/adjure/base/config)"
  exit 1
fi

if is_upgrade ; then
    if [ -f $INSTALL_PRESERVE_DIR/class_persist.config ] ; then
        source $INSTALL_PRESERVE_DIR/class_persist.config
    fi
    class_level=$ADJURE_BASE_CLASS_LEVEL
    echo "Restoring Classification Level: $class_level"
else
    class_level="INVALID"
fi

while invalid_classlevel $class_level; do
  read -p "Please select install classification level (U, C, S, TS) [$ADJURE_BASE_CLASS_LEVEL]: " class_level
  if [[ -z "$class_level" ]]; then
    class_level=$ADJURE_BASE_CLASS_LEVEL
  fi
done

if is_upgrade ; then
    compartment_array=(${ADJURE_BASE_CLASS_COMPART[@]})
    echo "Restoring Compartments: ${ADJURE_BASE_CLASS_COMPART[*]}"
else
    compartment_array=("INVALID")
fi
while invalid_compartment ${compartment_array[*]}; do
  read -p "Please select install compartments, space-delimited, NONE for none if default is set (SI, TK) [${ADJURE_BASE_CLASS_COMPART[*]}]: " compartment
  if [[ -z "$compartment" ]]; then
    compartment=${ADJURE_BASE_CLASS_COMPART[*]}
  fi
  if [[ "$compartment" == "NONE" ]]; then
    compartment_array=()
  else
    i=0
    compartment_array=()
    for c in $compartment; do
      compartment_array[$i]=$c
      i=$(( $i + 1 ))
    done
  fi
done

if is_upgrade ; then
    releaseto_array=(${ADJURE_BASE_CLASS_RELEASETO[@]})
    echo "Restoring Release To: ${ADJURE_BASE_CLASS_RELEASETO[*]}"
else
    releaseto_array=("INVALID")
fi

while invalid_releaseto ${releaseto_array[*]}; do
  read -p "Please select install releaseto, space-delimited, NONE for none if default is set (FOUO, ROK, EY3, EY4, EY5, EY9, NOFOR) [${ADJURE_BASE_CLASS_RELEASETO[*]}]: " releaseto
  if [[ -z "$releaseto" ]]; then
    releaseto=${ADJURE_BASE_CLASS_RELEASETO[*]}
  fi
  if [[ "$releaseto" == "NONE" ]]; then
    releaseto_array=()
  else
    i=0
    releaseto_array=()
    for r in $releaseto; do
      releaseto_array[$i]=$r
      i=$(( $i + 1 ))
    done
  fi
done


# For now, no compartment_ext or releaseto_ext

ADJURE_BASE_CLASS_LEVEL=$class_level
ADJURE_BASE_CLASS_COMPART=( ${compartment_array[*]} )
ADJURE_BASE_CLASS_RELEASETO=( ${releaseto_array[*]} )

echo "Using class_level=$ADJURE_BASE_CLASS_LEVEL, compartment=${ADJURE_BASE_CLASS_COMPART[*]}, releaseto=${ADJURE_BASE_CLASS_RELEASETO[*]}"
declare -p ADJURE_BASE_CLASS_LEVEL ADJURE_BASE_CLASS_COMPART ADJURE_BASE_CLASS_RELEASETO ADJURE_BASE_CLASS_COMPART_EXT ADJURE_BASE_CLASS_RELEASETO_EXT > $script_path/class_persist.config


class_fname=$(mktemp)
class_fname_gen=$(mktemp)
cleanup() {
  rm -f $class_fname
  rm -f $class_fname_gen
}
trap cleanup EXIT

echo "dn: uniqueIdentifier=%%UUID:defaultClassification%%,ou=Classification,dc=ticom-geo,dc=com" > $class_fname
echo "objectClass: tgiClassificationObject" >> $class_fname
echo "classLevel: $ADJURE_BASE_CLASS_LEVEL" >> $class_fname
for c in ${ADJURE_BASE_CLASS_COMPART[*]}; do
  echo "classCompartment: $c" >> $class_fname
done
for c in ${ADJURE_BASE_CLASS_RELEASETO[*]}; do
  echo "classReleaseTo: $c" >> $class_fname
done

echo "Loading new classification object into LDAP"
python $script_path/../bin/fill_ldif_template.py -i $class_fname -o $class_fname_gen
bash $script_path/../bin/add_ldif_to_ldap.sh $class_fname_gen

set_adjure_base_config ADJURE_CLASSIFICATION_INFO "$script_path/class_persist.config"
set_adjure_base_config ADJURE_LOCAL_HOST $(hostname)
