########
# Data
########

#
# host name
#
dn: uniqueIdentifier=b51245d2-ee3f-11eb-ad7a-57820c39941d,ou=Host,dc=ticom-geo,dc=com
objectclass: tgiHostObject
hostName: localhost.localdomain
classificationRefId: uniqueIdentifier=b51249e2-ee3f-11eb-ad7a-57820c39941d,ou=Classification,dc=ticom-geo,dc=com

# TODO: Not done converting things over to startup
dn: uniqueIdentifier=b5124c76-ee3f-11eb-ad7a-57820c39941d,ou=StartupConfig,dc=ticom-geo,dc=com
objectClass: tgiStartupObject
startupType: disabled
startupRequested: FALSE
startupConfigRefId: uniqueIdentifier=b5124eec-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
cn: dummy startup object for things that haven't been converted yet

# config for disabled startup
dn: uniqueIdentifier=b5124eec-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: dummy config for a disabled startup

#
# default msg bus
#
dn: uniqueIdentifier=b512519e-ee3f-11eb-ad7a-57820c39941d,ou=MessageBus,dc=ticom-geo,dc=com
cn: default_msg_bus
objectclass: tgiMessageBusObject
preferredBrokerName: anonymous_qpid
preferredBrokerConfigRefId: uniqueIdentifier=b5125400-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com

#
#default msg bus config
#
dn: uniqueIdentifier=b5125400-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
cn: default localhost msgbus guest config
configVar: {wireProtocol}amqp
configVar: {transport}tcp
configVar: {host}localhost.localdomain
configVar: {port}5672
configVar: {cmdNodeName}amq.topic
configVar: {eventNodeName}amq.topic
configVar: {publishreplyNodeName}amq.topic
objectClass: tgiConfigObject

#
# default logger object
#
dn: uniqueIdentifier=b51256e4-ee3f-11eb-ad7a-57820c39941d,ou=Logger, dc=ticom-geo,dc=com
loggerConfigRefId: uniqueIdentifier=b5125928-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
loggerName: log4cxx
objectClass: tgiLoggerObject
cn: default logger (logs to console)

#
# default logger configuraton
#
dn: uniqueIdentifier=b5125928-ee3f-11eb-ad7a-57820c39941d,ou=Config, dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {log4j.rootLogger}DEBUG, console
configVar: {log4j.appender.console}org.apache.log4j.ConsoleAppender
configVar: {log4j.appender.console.layout}org.apache.log4j.PatternLayout
configVar: {log4j.appender.console.layout.ConversionPattern}%d{ISO8601}{GMT} %-5p %-25c %30.30l - %m%n

#
# Startup Service
#
dn: uniqueIdentifier=b5125bf8-ee3f-11eb-ad7a-57820c39941d,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Startup Service
objectClass: tgiApplicationInstallationObject
configRefId: uniqueIdentifier=b5125e50-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
hostRefId: uniqueIdentifier=b51245d2-ee3f-11eb-ad7a-57820c39941d,ou=Host,dc=ticom-geo,dc=com
startupRefId: uniqueIdentifier=b5124c76-ee3f-11eb-ad7a-57820c39941d,ou=StartupConfig,dc=ticom-geo,dc=com
loggerInfoRefId: uniqueIdentifier=b51260b2-ee3f-11eb-ad7a-57820c39941d,ou=Logger,dc=ticom-geo,dc=com

dn: uniqueIdentifier=b5125e50-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: Startup Service Config
configVar: {workIntervalSec}3
configVar: {statusSocket}/tmp/adjure_startup.sock

# Startup Service (C++)
dn: uniqueIdentifier=b51260b2-ee3f-11eb-ad7a-57820c39941d,ou=Logger,dc=ticom-geo,dc=com
loggerConfigRefId: uniqueIdentifier=b5126350-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
loggerName: log4cxx
objectClass: tgiLoggerObject

dn: uniqueIdentifier=b5126350-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {log4j.rootLogger}DEBUG, file
configVar: {log4j.appender.file}org.apache.log4j.RollingFileAppender
configVar: {log4j.appender.file.file}${ADJURE_LOG_ROOT}/adjure_startup/adjure_startup.log
configVar: {log4j.appender.file.layout}org.apache.log4j.PatternLayout
configVar: {log4j.appender.file.layout.ConversionPattern}%d{ISO8601}{GMT} %-5p %-25c %30.30l - %m%n
configVar: {log4j.appender.file.MaxFileSize}20MB
configVar: {log4j.appender.file.MaxBackupIndex}100

#########################################
# Adjure-Java application common configuration objects
#########################################
#
# Shared configuration settings - Main Configuration
#
dn: uniqueIdentifier=b5126846-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: Common configuration

#
# service request timeout settings
#
dn: cn=Service,uniqueIdentifier=b5126846-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
cn: Common service request configuration
objectClass: tgiConfigObject
# deprecated
configVar: {request.timeout}60000

#
# session service (Session) settings
#
dn: cn=Session,uniqueIdentifier=b5126846-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
cn: Common Session service configuration
objectClass: tgiConfigObject
configVar: {expire.timeout}240000

#
# messaging settings
#
dn: cn=Messaging,uniqueIdentifier=b5126846-ee3f-11eb-ad7a-57820c39941d,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
cn: Common messaging configuration
configVar: {jms.provider}qpidjms
configVar: {qpid.jms.connection.username}guest
configVar: {qpid.jms.connection.password}guest
configVar: {request.timeoutms}60000

