#!/usr/bin/env bash
#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#
WHEREAMI=`dirname $0`
if [ -z "$QPID_HOME" ]; then
    export QPID_HOME=`cd $WHEREAMI/../ && pwd`
fi

if [ -z "$QPID_WORK" ]; then
    echo "Setting QPID_WORK to $HOME as default"
    QPID_WORK=$HOME
fi

# Set to help us get round the manifold problems of ps/pgrep on various
# platforms which gather up to prevent qpid_stop from working .....
if [ -z "$QPID_PNAME" ]; then
    export QPID_PNAME=" -DPNAME=QPBRKR"
fi

# Set classpath to include the jars in lib/, lib/plugins, and lib/opt
QPID_LIBS="$QPID_HOME/lib/*:$QPID_HOME/lib/plugins/*:$QPID_HOME/lib/opt/*"

# Set other variables used by the qpid-run script before calling
export JAVA=java \
       JAVA_VM=-server \
       JAVA_MEM="-Xmx2048m -XX:MaxDirectMemorySize=2048m" \
       JAVA_GC="-XX:+UseConcMarkSweepGC -XX:+HeapDumpOnOutOfMemoryError" \
       QPID_CLASSPATH=$QPID_LIBS \
       QPID_RUN_LOG=2 

# Echo the PID to file. Since qpid-run is sourced and uses exec to
# launch the broker, this will give the brokers PID.
if [ -z "$QPID_PID_FILENAME" ]; then
    export QPID_PID_FILENAME="qpid-server.pid"
fi
echo $$ > "${QPID_WORK}/${QPID_PID_FILENAME}"

. "${QPID_HOME}/bin/qpid-run" org.apache.qpid.server.Main "$@"
