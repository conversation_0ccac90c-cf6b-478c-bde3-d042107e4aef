#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#   http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

### EXAMPLE ACL V2 FILE
### NOTE: Rules are considered from top to bottom, and the first matching rule governs the decision.
### Rules may refer to users or groups. Groups are currently defined in the etc/groups file.

### JMX MANAGEMENT ####

# To use JMX management, first give the user/group ACCESS MANAGEMENT permission
ACL ALLOW administrators ACCESS MANAGEMENT
ACL ALLOW guest ACCESS MANAGEMENT

# Allow guest to perform read operations on the ServerInformation mbean
ACL ALLOW guest ACCESS METHOD component="ServerInformation"

# Allow 'administrators' all management operations. To reduce log file noise, only non-read-only operations are logged.
ACL ALLOW administrators ACCESS METHOD
ACL ALLOW-LOG administrators ALL METHOD

# Allow 'guest' to view logger levels, and use getter methods on LoggingManagement
ACL ALLOW guest ACCESS METHOD component="LoggingManagement" name="viewEffectiveRuntimeLoggerLevels"
ACL ALLOW guest ACCESS METHOD component="LoggingManagement" name="get*"

# Deny access to Shutdown, UserManagement, ConfigurationManagement and LoggingManagement for all other users
# You could grant specific users access to these beans by adding rules above to allow them
ACL DENY-LOG ALL ACCESS METHOD component="Shutdown"
ACL DENY-LOG ALL ACCESS METHOD component="UserManagement"
ACL DENY-LOG ALL ACCESS METHOD component="ConfigurationManagement"
ACL DENY-LOG ALL ACCESS METHOD component="LoggingManagement"

# Allow everyone to perform all read operations on the mbeans not listed in the DENY rules above
ACL ALLOW ALL ACCESS METHOD

### WEB MANAGEMENT ####

# To use web management, first give the user/group ACCESS MANAGEMENT permission
ACL ALLOW webadmins ACCESS MANAGEMENT

# ACL for web management console admins
# All rules below are required for console admin users
# to perform create/update/delete operations
ACL ALLOW-LOG webadmins CREATE QUEUE
ACL ALLOW-LOG webadmins UPDATE QUEUE
ACL ALLOW-LOG webadmins DELETE QUEUE
ACL ALLOW-LOG webadmins PURGE  QUEUE
ACL ALLOW-LOG webadmins CREATE EXCHANGE
ACL ALLOW-LOG webadmins DELETE EXCHANGE
ACL ALLOW-LOG webadmins BIND   EXCHANGE
ACL ALLOW-LOG webadmins UNBIND EXCHANGE
ACL ALLOW-LOG webadmins CREATE GROUP
ACL ALLOW-LOG webadmins DELETE GROUP
ACL ALLOW-LOG webadmins UPDATE GROUP
ACL ALLOW-LOG webadmins CREATE USER
ACL ALLOW-LOG webadmins DELETE USER
ACL ALLOW-LOG webadmins UPDATE USER

ACL ALLOW-LOG webadmins UPDATE METHOD

# authorise operations changing broker model
ACL ALLOW-LOG webadmins CONFIGURE BROKER

# authorise operations to view and download broker logs
ACL ALLOW webadmins ACCESS_LOGS BROKER

# at the moment only the following UPDATE METHOD rules are supported by web management console
#ACL ALLOW-LOG webadmins UPDATE METHOD component="VirtualHost.Queue" name="moveMessages"
#ACL ALLOW-LOG webadmins UPDATE METHOD component="VirtualHost.Queue" name="copyMessages"
#ACL ALLOW-LOG webadmins UPDATE METHOD component="VirtualHost.Queue" name="deleteMessages"
#ACL ALLOW-LOG webadmins UPDATE METHOD component="VirtualHost.Queue" name="clearQueue"

### MESSAGING ###
# The 'ACCESS VIRTUALHOST' rules below apply to messaging operations (as opposed to management operations)

# Firewall examples

# Deny access to all users from *.example.company1.com and *.example.company2.com
ACL DENY-LOG all ACCESS VIRTUALHOST from_hostname=".*\.example\.company1.com,.*\.example\.company2.com"

# Deny access to all users in the IP ranges ***********-************* and ***********-*************,
# using the notation specified in RFC 4632, "Classless Inter-domain Routing (CIDR)"
ACL DENY-LOG messaging-users ACCESS VIRTUALHOST from_network="***********/24,***********/24"

# Deny access to all users in the IP ranges ***********-************* and ***********-*************,
# using wildcard notation.
ACL DENY-LOG messaging-users ACCESS VIRTUALHOST from_network="192.169.1.*,192.169.2.*"

# Allow 'messaging-users' group to connect to all virtualhosts
ACL ALLOW-LOG messaging-users ACCESS VIRTUALHOST

# Deny messaging-users management
ACL DENY-LOG messaging-users ACCESS MANAGEMENT


# Client side
# Allow the 'client' user to publish requests to the request queue and create, consume from, and delete temporary reply queues.
ACL ALLOW-LOG client CREATE QUEUE temporary="true"
ACL ALLOW-LOG client CONSUME QUEUE temporary="true"
ACL ALLOW-LOG client DELETE QUEUE temporary="true"
ACL ALLOW-LOG client BIND EXCHANGE name="amq.direct" temporary="true"
ACL ALLOW-LOG client UNBIND EXCHANGE name="amq.direct" temporary="true"
ACL ALLOW-LOG client PUBLISH EXCHANGE name="amq.direct" routingKey="example.RequestQueue"

# Server side
# Allow the 'server' user to create and consume from the request queue and publish a response to the temporary response queue created by
# client.
ACL ALLOW-LOG server CREATE QUEUE name="example.RequestQueue"
ACL ALLOW-LOG server CONSUME QUEUE name="example.RequestQueue"
ACL ALLOW-LOG server BIND EXCHANGE
ACL ALLOW-LOG server PUBLISH EXCHANGE name="amq.direct" routingKey="TempQueue*"


### DEFAULT ###

# Deny all users from performing all operations
ACL DENY-LOG all all
