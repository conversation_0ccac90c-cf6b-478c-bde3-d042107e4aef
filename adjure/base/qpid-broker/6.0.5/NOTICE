Apache Qpid Broker for Java
Copyright 2006-2016 The Apache Software Foundation

This product includes software developed at
The Apache Software Foundation (http://www.apache.org/).

###############################################

Apache Derby
Copyright 2004-2014 The Apache Software Foundation

Portions of Derby were originally developed by
International Business Machines Corporation and are
licensed to the Apache Software Foundation under the
"Software Grant and Corporate Contribution License Agreement",
informally known as the "Derby CLA".
The following copyright notice(s) were affixed to portions of the code
with which this file is now or was at one time distributed
and are placed here unaltered.

(C) Copyright 1997,2004 International Business Machines Corporation.  All rights reserved.

(C) Copyright IBM Corp. 2003.

###############################################

==============================================================
 Jetty Web Container
 Copyright 1995-2014 Mort Bay Consulting Pty Ltd.
==============================================================

The Jetty Web Container is Copyright Mort Bay Consulting Pty Ltd
unless otherwise noted.

Jetty is dual licensed under both

  * The Apache 2.0 License
    http://www.apache.org/licenses/LICENSE-2.0.html

      and

  * The Eclipse Public 1.0 License
    http://www.eclipse.org/legal/epl-v10.html

Jetty may be distributed under either license.

The javax.servlet package used was sourced from the Apache
Software Foundation and is distributed under the apache 2.0
license.

The UnixCrypt.java code implements the one way cryptography used by
Unix systems for simple password protection.  Copyright 1996 Aki Yoshida,
modified April 2001  by Iris Van den Broeke, Daniel Deville.
Permission to use, copy, modify and distribute UnixCrypt
for non-commercial or commercial purposes and without fee is
granted provided that the copyright notice appears in all copies.

###############################################

Geronimo Servlet Spec 3.0
Copyright (c) 2003-2010 The Apache Software Foundation

This product includes software developed by
The W3C Consortium (http://www.w3.org/).

Copyright (C) 1994-2002 World Wide Web Consortium,
(Massachusetts Institute of Technology, Institut National
de Recherche en Informatique et en Automatique, Keio
University). All Rights Reserved.
http://www.w3.org/Consortium/Legal/

###############################################

Apache Commons Codec
Copyright (c) 2002-2013 The Apache Software Foundation

###############################################

Apache Commons CLI
Copyright (c) 2001-2009 The Apache Software Foundation

###############################################

Apache Commons BCEL
Copyright (c) 2004-2015 The Apache Software Foundation

###############################################

Jackson JSON processor
Copyright (c) 2009-2011 FasterXML, LLC. All rights reserved

###############################################

Google Core Libraries for Java
Copyright (c) 2008-2014 The Guava Authors

###############################################

BoneCP Core Library
Copyright (c) 2009-2011 Wallace Wadge

###############################################

Dojo Toolkit
Copyright (c) 2005-2015, The Dojo Foundation

###############################################

slf4j-api
Copyright (c) 2004-2014 QOS.ch

###############################################

Logback
Copyright (c) 1999-2012, QOS.ch
