#!/bin/bash

RETVAL=0
prog="qpid-server"

user=$(whoami)
#if [[ "$user" != "root" ]]; then
#  echo "Must be root to start $prog"
#  exit 1
#fi

# enable cores
export DAEMON_COREFILE_LIMIT='unlimited'

# Source function library.
. /etc/init.d/functions

QPID_BASE=/h/adjure/base/qpid-broker
QPID_START=$QPID_BASE/6.0.5/bin/qpid-server
LOCK_FILE=$QPID_BASE/work/adjure-qpid-server
PID_FILE=$QPID_BASE/work/adjure-qpid-server.pid

export QPID_WORK=$QPID_BASE/work

start() {
    [ -x $QPID_START ] || exit 5
    echo -n $"Starting $prog: "
    nohup $QPID_START -sp $QPID_BASE/config/config.json > /dev/null 2>&1 &

    num_tries=0
    RETVAL=1
    while [[ "$num_tries" -lt 50 ]]; do
      usleep 100000
      num_tries=$((num_tries + 1 ))
      pid=$(pgrep -f org.apache.qpid.server.Main)
      if [[ "$pid" != "" ]]; then
        RETVAL=0
        echo $pid > $PID_FILE
        touch $LOCK_FILE
        break;
      fi
    done

    echo
    return $RETVAL
}

stop() {
    echo -n $"Stopping $prog: "
    pid=`cat $PID_FILE`

    killproc -p $PID_FILE $QPID_START -TERM
    RETVAL=$?

    # Wait until the monitor exits
    while (checkpid $pid)
    do
        usleep 100000
    done

    echo
    [ "$RETVAL" = 0 ] && rm -f $LOCK_FILE
    [ "$RETVAL" = 0 ] && rm -f $PID_FILE
    return $RETVAL
}

restart() {
        stop
        start
}

rh_status() {
    # run checks to determine if the service is running or use generic status
    status -p $PID_FILE $prog
    RETVAL=$?

    # Remap RETVAL to 1 on failure so we don't need to worry about what
    # status returns on failure
    if [[ "$RETVAL" != "0" ]]; then RETVAL=1; fi

    if [[ "$RETVAL" == 0 ]]; then
      nc -z localhost 5672 >/dev/null 2>&1
      # If the port isn't up, return 99 ("unknown")
      if [[ "$?" != "0" ]]; then RETVAL=99; fi
    fi

    return $RETVAL
}

rh_status_q() {
    rh_status >/dev/null 2>&1
}

case "$1" in
    start)
        rh_status_q && exit 0
        $1
        ;;

    stop)
        rh_status_q || exit 0
        $1
        ;;

    restart)
        $1
        ;;

    status)
        rh_status
        ;;

    *)
    echo $"Usage: $0 {start|stop|status|restart}"
    exit 2
esac
exit $?
