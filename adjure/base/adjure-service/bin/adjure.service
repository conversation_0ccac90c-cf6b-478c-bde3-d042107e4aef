[Unit]
Description=Adjure Application Control Service
#
# Although some applications might have additional dependencies (such as mysql), this service
# actually just depends on network.target, and slapd.service.
#
After=network.target
After=slapd.service

[Service]
WorkingDirectory=/h/adjure/base/adjure-service/bin

#
# Set this to ensure we have enough processes/threads
# This should be the same value as specified in adjure-limits.conf
#
LimitNPROC=3072

#
# Set stdout to use the systemd journal file
#
StandardOutput=journal

#
# Write to the same file as stdout writes to
#
StandardError=inherit

#
# simple = started after service process has been forked off
# forking= started after service process has been forked off and after ExecStart application has
#           been spawned and application has called fork()
#           Use PIDFile with this type
Type=simple

#
# mixed = SIGTERM goes only to main process, final SIGKILL goes to all processes in the process group
#
KillMode=mixed

#
# amount of time to wait for service to stop after the initial SIGTERM is sent, before sending the final SIGKILL
# wait 5 minutes
#
TimeoutStopSec=300

#
# initial signal to send (SIGTERM)
#
KillSignal=SIGTERM

#
# The script to run to start the service
#
ExecStart=/h/adjure/base/adjure-service/bin/adjure_service_start exec
#
# The script to run to stop the service
#
ExecStopPost=/h/adjure/base/adjure-service/bin/adjure_post_stop

#
# Run the service as opsys
#
User=opsys

[Install]
#
# Although we don't want to start this service automatically by default, we DO
# want to state which targets it *should* be enabled for, if someone does enable
# the service. The only target we want it enabled for is multi-user.target
#
WantedBy=multi-user.target
