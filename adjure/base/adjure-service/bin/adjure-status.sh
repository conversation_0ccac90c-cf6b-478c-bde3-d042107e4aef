#!/bin/bash

. /etc/sysconfig/adjure
. /h/adjure/base/config/adjure_env.sh

# RHEL 7
if [[ "a$ADJURE_SERVICE_ACCESS_PORT" == "a" ]]; then
  $ADJURE_SERVICE_ACCESS_PORT=10040
fi

if [[ "$1" == "--show-uuid" ]]; then
  status_data=$(echo "status" | nc localhost $ADJURE_SERVICE_ACCESS_PORT)
else
  status_data=$(echo "status_no_uuid" | nc localhost $ADJURE_SERVICE_ACCESS_PORT)
fi

if [ $? = 0 ]; then
  echo "$status_data"
else
  echo "adjure is stopped"
fi
