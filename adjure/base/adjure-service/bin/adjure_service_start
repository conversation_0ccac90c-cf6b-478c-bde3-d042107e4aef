#!/bin/bash

function usage() {
  echo $"Usage: $0 {start|stop|status|restart|condrestart|try-restart|reload|force-reload}"
  echo "   Status return code 1 means try again later. 2 means failed, 0 means success."
}

#
# $1 is the mode to use when starting
# $1==exec means non-daemon
# $1==forking means daemon
if [ "$1" = "" ]; then
  usage
  exit 2
fi

mode=$1

#
# Setup the environment
#
. /etc/sysconfig/adjure
. /h/adjure/base/config/adjure_env.sh

#
# Verify the environment
#
if [ -z "$STARTUP_APP_REF_ID" ]; then
  err_str="STARTUP_APP_REF_ID must be defined in /etc/sysconfig/adjure"
  echo $err_str
  echo $err_str > $ERROR_FILE 2>/dev/null
  exit 2
fi

if [ -z "$ADJURE_BASEDIR" ]; then
  err_str="ADJURE_BASEDIR must be defined in /h/adjure/base/config/adjure_env.sh"
  echo $err_str
  echo $err_str > $ERROR_FILE 2>/dev/null
  exit 2
fi

export ADJURESERVICE_APPINSTALL_UUID="$STARTUP_APP_REF_ID"
export ADJURESERVICE_CFG_FORMAT=registry

SERVICE_BIN="$ADJURE_SERVICE_DIR/bin"
SERVICE_CFG="$ADJURE_SERVICE_DIR/config"
SERVICE_WORK="$ADJURE_SERVICE_DIR/work"

jvm_debug_opts=""
if [[ "$ADJURE_SERVICE_DEBUG" == "true" ]]; then
  if [[ "a$ADJURE_SERVICE_DEBUG_PORT" == "a" ]]; then
    ADJURE_SERVICE_DEBUG_PORT=8785
  fi
  jvm_debug_opts="-agentlib:jdwp=transport=dt_socket,address=${ADJURE_SERVICE_DEBUG_PORT},server=y,suspend=n"
fi

use_java_home="$JAVA_HOME"
if [ -z "$JAVA_HOME" ]; then
  use_java_home=/usr/java/default
fi

use_java="$ADJURE_SERVICE_JAVA"
if [ -z "$use_java" ]; then
  use_java="$use_java_home/bin/java"
fi

jvm_memory_opts="-Xms128m -Xmx256m -XX:MetaspaceSize=96m"

if [ -z "$ADJURE_SERVICE_ACCESS_PORT" ]; then
  $ADJURE_SERVICE_ACCESS_PORT=10040
fi

app_and_arguments="org.jboss.weld.environment.se.StartMain --serviceport $ADJURE_SERVICE_ACCESS_PORT"

mkdir -p "${ADJURE_LOG_ROOT}/adjure-service"
if [[ "$mode" == "forking" ]]; then
  exec "${use_java}" ${jvm_memory_opts} ${jvm_debug_opts} -Dfile.encoding=UTF-8 -cp "$SERVICE_BIN/adjure-service.jar:$SERVICE_CFG" ${app_and_arguments} >${ADJURE_LOG_ROOT}/adjure-service/console.log 2>&1 &
else
  exec "${use_java}" ${jvm_memory_opts} ${jvm_debug_opts} -Dfile.encoding=UTF-8 -cp "$SERVICE_BIN/adjure-service.jar:$SERVICE_CFG" ${app_and_arguments} >${ADJURE_LOG_ROOT}/adjure-service/console.log 2>&1
fi
exit $?