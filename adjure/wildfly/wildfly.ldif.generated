
#
# default Startup Config for all standard (non-exploded) deployments
# increase defaults for initial status interval and periodic status interval because
# the jboss-cli calls that they turn into take some time and I don't want to spam the system if we
# have a bunch of deployments. 
#
dn: uniqueIdentifier=cc68920e-ee3f-11eb-a991-e15ac11df92b,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {serviceScriptPath}/h/adjure-jboss/adjure-extras/private-tools/deploy_standard_ctrl
configVar: {workingDirectory}/h/adjure-jboss/adjure-extras/private-tools
# TODO: Should claim this as a failed status return code, but we'll let the timeout deal with it
# because sometimes we get a "failure" before the success but after we deploy, even though it should
# just return 1 to indicate it doesn't know the result yet.
# Since we're doing this, we'll lower the timeout time to a more reasonable (but still large) 3min
#configVar: {failedStatusReturnCode}2
# Note: these status intervals are VERY LOW if you switch to the jboss cli, but the default
# deployment scripts (deploy_standard_ctrl and deploy_exploded_ctrl) work well with them.
configVar: {initialStatusInterval}3
configVar: {periodicStatusInterval}20
configVar: {timeoutTime}180
configVar: {unknownStatusReturnCode}1
cn: default startup config for wildfly standard deployments

#
# default Startup Config for all exploded deployments
# increase defaults for initial status interval and periodic status interval because
# the jboss-cli calls that they turn into take some time and I don't want to spam the system if we
# have a bunch of deployments.
#
dn: uniqueIdentifier=cc689628-ee3f-11eb-a991-e15ac11df92b,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {serviceScriptPath}/h/adjure-jboss/adjure-extras/private-tools/deploy_exploded_ctrl
configVar: {workingDirectory}/h/adjure-jboss/adjure-extras/private-tools
# TODO: Should claim this as a failed status return code, but we'll let the timeout deal with it
# because sometimes we get a "failure" before the success but after we deploy, even though it should
# just return 1 to indicate it doesn't know the result yet.
# Since we're doing this, we'll lower the timeout time to a more reasonable (but still large) 3min
#configVar: {failedStatusReturnCode}2
# Note: these status intervals are VERY LOW if you switch to the jboss cli, but the default
# deployment scripts (deploy_standard_ctrl and deploy_exploded_ctrl) work well with them.
configVar: {initialStatusInterval}3
configVar: {periodicStatusInterval}20
configVar: {timeoutTime}180
configVar: {unknownStatusReturnCode}1
cn: default startup config for wildfly exploded deployments

#------------------------------------------------------------------------------------------------------------------------------------------
# You can use this file as a template for creating the LDAP ldif entries that define the information the startup service uses to launch
# your application
#------------------------------------------------------------------------------------------------------------------------------------------
#
# __applicationIdParamPrefix__ - replaced with the name you use in your other application ldif templates to refer to UUID values for your
#                                application. For example, if your application is the session server, you may use sessionServerInstallation,
#                                sessionServerConfig, sessionServerApplication, sessionServerStartup, etc. With this example, you would use
#                                sessionServer as the value for __applicationIdParamPrefix__
# __startupExecutable__ - the full path of the executable to start for this application
# __applicationLongName__ - the logical name of the application useful in comments and descriptions, but not generally for lookup. In the
#                           above example, you might use 'Session Server' as the value for this

#
# Wildfly (21.0.2) Server - Startup configuration metadata
#
dn: uniqueIdentifier=cc6899a2-ee3f-11eb-a991-e15ac11df92b,ou=StartupConfig,dc=ticom-geo,dc=com
objectClass: tgiStartupObject
startupRequested: TRUE
startupType: service
startupConfigRefId: uniqueIdentifier=cc689d44-ee3f-11eb-a991-e15ac11df92b,ou=Config,dc=ticom-geo,dc=com
startupApplicationInstallationDependency: uniqueIdentifier=b51f6b04-ee3f-11eb-93c6-edd4a0bf3b9a,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Wildfly (21.0.2) Server Startup Config Metadata

#
# Wildfly 21.0.2 Server - Startup configuration settings
#
dn: uniqueIdentifier=cc689d44-ee3f-11eb-a991-e15ac11df92b,ou=Config,dc=ticom-geo,dc=com
objectClass: tgiConfigObject
configVar: {serviceScriptPath}/h/adjure-jboss/adjure-extras/private-tools/wildfly_ctrl
configVar: {workingDirectory}/h/adjure-jboss/wildfly21/bin
configVar: {unknownStatusReturnCode}1
cn: Wildfly (21.0.2) Server Startup Config Settings

#------------------------------------------------------------------------------------------------------------------------------------------
# You can use this file as a template for creating the LDAP ldif entries that define the application and application installation. These
# entries reference application configuration entries, startup configuration, bus service entries, etc.
#------------------------------------------------------------------------------------------------------------------------------------------
#
# __applicationIdParamPrefix__ - replaced with the name you use in your other application ldif templates to refer to UUID values for your
#                                application. For example, if your application is the session server, you may use sessionServerInstallation,
#                                sessionServerConfig, sessionServerApplication, sessionServerStartup, etc. With this example, you would use
#                                sessionServer as the value for __applicationIdParamPrefix__
# __applicationLongName__ - the logical name of the application useful in comments and descriptions, but not generally for lookup. In the
#                           above example, you might use 'Session Server' as the value for this
# __applicationSimpleName__ - the simple logical name of the application. This is used in situations where no spaces are allowed, though
#                             the use of upper case, lower case, and numeric is fine. In the above example, you might use 'SessionServer'
#                             as the value for this
# __applicationMajorVersion__ - the major version for your application
# __applicationMinorVersion__ - the minor version for your application
# __applicationPatchVersion__ - the patch version for your application

######################################################
# Adjure Wildfly (21.0.2) Server
######################################################
#
# Adjure Wildfly (21.0.2) Server - Application
#
dn: uniqueIdentifier=cc68a0fa-ee3f-11eb-a991-e15ac11df92b,ou=Application,dc=ticom-geo,dc=com
cn: Wildfly (21.0.2) Server Application
objectClass: tgiApplicationObject
applicationName: wildflyServer
majorVersion: 1
minorVersion: 0
patchVersion: 0

#
# Adjure Wildfly (21.0.2) Server - Application Installation
#
dn: uniqueIdentifier=cc68a3a2-ee3f-11eb-a991-e15ac11df92b,ou=ApplicationInstallation,dc=ticom-geo,dc=com
cn: Wildfly (21.0.2) Server
objectClass: tgiApplicationInstallationObject
hostRefId: uniqueIdentifier=b51245d2-ee3f-11eb-ad7a-57820c39941d,ou=Host,dc=ticom-geo,dc=com
startupRefId: uniqueIdentifier=cc6899a2-ee3f-11eb-a991-e15ac11df92b,ou=StartupConfig,dc=ticom-geo,dc=com

